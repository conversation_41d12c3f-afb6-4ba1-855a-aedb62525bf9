{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["HELLO\n"]}], "source": ["print(\"HELLO\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data...\n"]}], "source": ["import threading\n", "\n", "def task():\n", "    print(\"Loading data...\")\n", "\n", "thread = threading.Thread(target=task)\n", "thread.start()\n", "thread.join()\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([1, 2, 3])\n", "tensor([[0.0782, 0.1368, 0.4284],\n", "        [0.8000, 0.7849, 0.9950]])\n"]}], "source": ["import torch\n", "\n", "# Creating tensors from lists\n", "tensor_a = torch.tensor([1, 2, 3])\n", "print(tensor_a)\n", "\n", "# Creating tensors with random values\n", "tensor_b = torch.rand((2, 3))  # 2 rows, 3 columns\n", "print(tensor_b)\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "2025-03-13 21:16:16,625\tINFO util.py:154 -- Missing packages: ['ipywidgets']. Run `pip install -U ipywidgets`, then restart the notebook server for rich notebook output.\n"]}, {"ename": "OSError", "evalue": "[Errno 0] AssignProcessToJobObject() failed", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mOSError\u001b[0m                                   Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[8], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[39mimport\u001b[39;00m \u001b[39mray\u001b[39;00m\n\u001b[1;32m----> 3\u001b[0m ray\u001b[39m.\u001b[39;49minit()  \u001b[39m# Initialize Ray\u001b[39;00m\n\u001b[0;32m      5\u001b[0m \u001b[39m@ray\u001b[39m\u001b[39m.\u001b[39mremote  \u001b[39m# Turns this function into a Ray Task\u001b[39;00m\n\u001b[0;32m      6\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mtrain_model\u001b[39m(epoch):\n\u001b[0;32m      7\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mTraining epoch \u001b[39m\u001b[39m{\u001b[39;00mepoch\u001b[39m}\u001b[39;00m\u001b[39m completed\u001b[39m\u001b[39m\"\u001b[39m\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\ray\\_private\\client_mode_hook.py:103\u001b[0m, in \u001b[0;36mclient_mode_hook.<locals>.wrapper\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m    101\u001b[0m     \u001b[39mif\u001b[39;00m func\u001b[39m.\u001b[39m\u001b[39m__name__\u001b[39m \u001b[39m!=\u001b[39m \u001b[39m\"\u001b[39m\u001b[39minit\u001b[39m\u001b[39m\"\u001b[39m \u001b[39mor\u001b[39;00m is_client_mode_enabled_by_default:\n\u001b[0;32m    102\u001b[0m         \u001b[39mreturn\u001b[39;00m \u001b[39mgetattr\u001b[39m(ray, func\u001b[39m.\u001b[39m\u001b[39m__name__\u001b[39m)(\u001b[39m*\u001b[39margs, \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mkwargs)\n\u001b[1;32m--> 103\u001b[0m \u001b[39mreturn\u001b[39;00m func(\u001b[39m*\u001b[39margs, \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mkwargs)\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\ray\\_private\\worker.py:1733\u001b[0m, in \u001b[0;36minit\u001b[1;34m(address, num_cpus, num_gpus, resources, labels, object_store_memory, local_mode, ignore_reinit_error, include_dashboard, dashboard_host, dashboard_port, job_config, configure_logging, logging_level, logging_format, logging_config, log_to_driver, namespace, runtime_env, storage, **kwargs)\u001b[0m\n\u001b[0;32m   1699\u001b[0m     ray_params \u001b[39m=\u001b[39m ray\u001b[39m.\u001b[39m_private\u001b[39m.\u001b[39mparameter\u001b[39m.\u001b[39mRayParams(\n\u001b[0;32m   1700\u001b[0m         node_ip_address\u001b[39m=\u001b[39m_node_ip_address,\n\u001b[0;32m   1701\u001b[0m         object_ref_seed\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1727\u001b[0m         node_name\u001b[39m=\u001b[39m_node_name,\n\u001b[0;32m   1728\u001b[0m     )\n\u001b[0;32m   1729\u001b[0m     \u001b[39m# Start the Ray processes. We set shutdown_at_exit=False because we\u001b[39;00m\n\u001b[0;32m   1730\u001b[0m     \u001b[39m# shutdown the node in the ray.shutdown call that happens in the atexit\u001b[39;00m\n\u001b[0;32m   1731\u001b[0m     \u001b[39m# handler. We still spawn a reaper process in case the atexit handler\u001b[39;00m\n\u001b[0;32m   1732\u001b[0m     \u001b[39m# isn't called.\u001b[39;00m\n\u001b[1;32m-> 1733\u001b[0m     _global_node \u001b[39m=\u001b[39m ray\u001b[39m.\u001b[39;49m_private\u001b[39m.\u001b[39;49mnode\u001b[39m.\u001b[39;49mNode(\n\u001b[0;32m   1734\u001b[0m         ray_params\u001b[39m=\u001b[39;49mray_params,\n\u001b[0;32m   1735\u001b[0m         head\u001b[39m=\u001b[39;49m\u001b[39mTrue\u001b[39;49;00m,\n\u001b[0;32m   1736\u001b[0m         shutdown_at_exit\u001b[39m=\u001b[39;49m\u001b[39mFalse\u001b[39;49;00m,\n\u001b[0;32m   1737\u001b[0m         spawn_reaper\u001b[39m=\u001b[39;49m\u001b[39mTrue\u001b[39;49;00m,\n\u001b[0;32m   1738\u001b[0m         ray_init_cluster\u001b[39m=\u001b[39;49m\u001b[39mTrue\u001b[39;49;00m,\n\u001b[0;32m   1739\u001b[0m     )\n\u001b[0;32m   1740\u001b[0m \u001b[39melse\u001b[39;00m:\n\u001b[0;32m   1741\u001b[0m     \u001b[39m# In this case, we are connecting to an existing cluster.\u001b[39;00m\n\u001b[0;32m   1742\u001b[0m     \u001b[39mif\u001b[39;00m num_cpus \u001b[39mis\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39mNone\u001b[39;00m \u001b[39mor\u001b[39;00m num_gpus \u001b[39mis\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39mNone\u001b[39;00m:\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\ray\\_private\\node.py:336\u001b[0m, in \u001b[0;36mNode.__init__\u001b[1;34m(self, ray_params, head, shutdown_at_exit, spawn_reaper, connect_only, default_worker, ray_init_cluster)\u001b[0m\n\u001b[0;32m    334\u001b[0m \u001b[39m# Start processes.\u001b[39;00m\n\u001b[0;32m    335\u001b[0m \u001b[39mif\u001b[39;00m head:\n\u001b[1;32m--> 336\u001b[0m     \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mstart_head_processes()\n\u001b[0;32m    338\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mnot\u001b[39;00m connect_only:\n\u001b[0;32m    339\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mstart_ray_processes()\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\ray\\_private\\node.py:1412\u001b[0m, in \u001b[0;36mNode.start_head_processes\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1409\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_write_cluster_info_to_kv()\n\u001b[0;32m   1411\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_ray_params\u001b[39m.\u001b[39mno_monitor:\n\u001b[1;32m-> 1412\u001b[0m     \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mstart_monitor()\n\u001b[0;32m   1414\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_ray_params\u001b[39m.\u001b[39mray_client_server_port:\n\u001b[0;32m   1415\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mstart_ray_client_server()\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\ray\\_private\\node.py:1305\u001b[0m, in \u001b[0;36mNode.start_monitor\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1302\u001b[0m \u001b[39mfrom\u001b[39;00m \u001b[39mray\u001b[39;00m\u001b[39m.\u001b[39;00m\u001b[39mautoscaler\u001b[39;00m\u001b[39m.\u001b[39;00m\u001b[39mv2\u001b[39;00m\u001b[39m.\u001b[39;00m\u001b[39mutils\u001b[39;00m \u001b[39mimport\u001b[39;00m is_autoscaler_v2\n\u001b[0;32m   1304\u001b[0m stdout_file, stderr_file \u001b[39m=\u001b[39m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mget_log_file_handles(\u001b[39m\"\u001b[39m\u001b[39mmonitor\u001b[39m\u001b[39m\"\u001b[39m, unique\u001b[39m=\u001b[39m\u001b[39mTrue\u001b[39;00m)\n\u001b[1;32m-> 1305\u001b[0m process_info \u001b[39m=\u001b[39m ray\u001b[39m.\u001b[39;49m_private\u001b[39m.\u001b[39;49mservices\u001b[39m.\u001b[39;49mstart_monitor(\n\u001b[0;32m   1306\u001b[0m     \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mgcs_address,\n\u001b[0;32m   1307\u001b[0m     \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_logs_dir,\n\u001b[0;32m   1308\u001b[0m     stdout_file\u001b[39m=\u001b[39;49mstdout_file,\n\u001b[0;32m   1309\u001b[0m     stderr_file\u001b[39m=\u001b[39;49mstderr_file,\n\u001b[0;32m   1310\u001b[0m     autoscaling_config\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_ray_params\u001b[39m.\u001b[39;49mautoscaling_config,\n\u001b[0;32m   1311\u001b[0m     fate_share\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mkernel_fate_share,\n\u001b[0;32m   1312\u001b[0m     max_bytes\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mmax_bytes,\n\u001b[0;32m   1313\u001b[0m     backup_count\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mbackup_count,\n\u001b[0;32m   1314\u001b[0m     monitor_ip\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_node_ip_address,\n\u001b[0;32m   1315\u001b[0m     autoscaler_v2\u001b[39m=\u001b[39;49mis_autoscaler_v2(fetch_from_server\u001b[39m=\u001b[39;49m\u001b[39mTrue\u001b[39;49;00m),\n\u001b[0;32m   1316\u001b[0m )\n\u001b[0;32m   1317\u001b[0m \u001b[39massert\u001b[39;00m ray_constants\u001b[39m.\u001b[39mPROCESS_TYPE_MONITOR \u001b[39mnot\u001b[39;00m \u001b[39min\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mall_processes\n\u001b[0;32m   1318\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mall_processes[ray_constants\u001b[39m.\u001b[39mPROCESS_TYPE_MONITOR] \u001b[39m=\u001b[39m [process_info]\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\ray\\_private\\services.py:2208\u001b[0m, in \u001b[0;36mstart_monitor\u001b[1;34m(gcs_address, logs_dir, stdout_file, stderr_file, autoscaling_config, fate_share, max_bytes, backup_count, monitor_ip, autoscaler_v2)\u001b[0m\n\u001b[0;32m   2206\u001b[0m \u001b[39mif\u001b[39;00m monitor_ip:\n\u001b[0;32m   2207\u001b[0m     command\u001b[39m.\u001b[39mappend(\u001b[39m\"\u001b[39m\u001b[39m--monitor-ip=\u001b[39m\u001b[39m\"\u001b[39m \u001b[39m+\u001b[39m monitor_ip)\n\u001b[1;32m-> 2208\u001b[0m process_info \u001b[39m=\u001b[39m start_ray_process(\n\u001b[0;32m   2209\u001b[0m     command,\n\u001b[0;32m   2210\u001b[0m     ray_constants\u001b[39m.\u001b[39;49mPROCESS_TYPE_MONITOR,\n\u001b[0;32m   2211\u001b[0m     stdout_file\u001b[39m=\u001b[39;49mstdout_file,\n\u001b[0;32m   2212\u001b[0m     stderr_file\u001b[39m=\u001b[39;49mstderr_file,\n\u001b[0;32m   2213\u001b[0m     fate_share\u001b[39m=\u001b[39;49mfate_share,\n\u001b[0;32m   2214\u001b[0m )\n\u001b[0;32m   2215\u001b[0m \u001b[39mreturn\u001b[39;00m process_info\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\ray\\_private\\services.py:1030\u001b[0m, in \u001b[0;36mstart_ray_process\u001b[1;34m(command, process_type, fate_share, env_updates, cwd, use_valgrind, use_gdb, use_valgrind_profiler, use_perftools_profiler, use_tmux, stdout_file, stderr_file, pipe_stdin)\u001b[0m\n\u001b[0;32m   1028\u001b[0m \u001b[39mif\u001b[39;00m win32_fate_sharing:\n\u001b[0;32m   1029\u001b[0m     \u001b[39mtry\u001b[39;00m:\n\u001b[1;32m-> 1030\u001b[0m         ray\u001b[39m.\u001b[39;49m_private\u001b[39m.\u001b[39;49mutils\u001b[39m.\u001b[39;49mset_kill_child_on_death_win32(process)\n\u001b[0;32m   1031\u001b[0m         psutil\u001b[39m.\u001b[39mProcess(process\u001b[39m.\u001b[39mpid)\u001b[39m.\u001b[39mresume()\n\u001b[0;32m   1032\u001b[0m     \u001b[39mexcept\u001b[39;00m (psutil\u001b[39m.\u001b[39mError, \u001b[39mOSError\u001b[39;00m):\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\ray\\_private\\utils.py:921\u001b[0m, in \u001b[0;36mset_kill_child_on_death_win32\u001b[1;34m(child_proc)\u001b[0m\n\u001b[0;32m    918\u001b[0m     \u001b[39mif\u001b[39;00m \u001b[39mnot\u001b[39;00m win32_AssignProcessToJobObject(win32_job, \u001b[39mint\u001b[39m(child_proc)):\n\u001b[0;32m    919\u001b[0m         \u001b[39mimport\u001b[39;00m \u001b[39mctypes\u001b[39;00m\n\u001b[1;32m--> 921\u001b[0m         \u001b[39mraise\u001b[39;00m \u001b[39mOSError\u001b[39;00m(ctypes\u001b[39m.\u001b[39mget_last_error(), \u001b[39m\"\u001b[39m\u001b[39mAssignProcessToJobObject() failed\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[0;32m    922\u001b[0m \u001b[39melse\u001b[39;00m:\n\u001b[0;32m    923\u001b[0m     \u001b[39massert\u001b[39;00m \u001b[39mFalse\u001b[39;00m, \u001b[39m\"\u001b[39m\u001b[39mAssignProcessToJobObject used despite being unavailable\u001b[39m\u001b[39m\"\u001b[39m\n", "\u001b[1;31mOSError\u001b[0m: [Errno 0] AssignProcessToJobObject() failed"]}], "source": ["import ray\n", "\n", "ray.init()  # Initialize Ray\n", "\n", "@ray.remote  # Turns this function into a Ray Task\n", "def train_model(epoch):\n", "    return f\"Training epoch {epoch} completed\"\n", "\n", "results = ray.get([train_model.remote(i) for i in range(5)])\n", "print(results)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "5a4ee9caf3efff372bffc064d50b3d6f485b7d609790dc31d6c93dfc1f3be82d"}}}, "nbformat": 4, "nbformat_minor": 2}