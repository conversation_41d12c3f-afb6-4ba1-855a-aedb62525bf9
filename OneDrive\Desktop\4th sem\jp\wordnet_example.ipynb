import nltk
from nltk.corpus import wordnet

# Download WordNet if not already downloaded
nltk.download('wordnet')

def get_synonyms_antonyms(word):
    synonyms = []
    antonyms = []
    
    # Iterate through each synset (set of synonyms) of the word
    for syn in wordnet.synsets(word):
        # Add lemma names (synonyms) to the list
        for lemma in syn.lemmas():
            # Add synonym if not already in list
            if lemma.name() != word and lemma.name() not in synonyms:
                synonyms.append(lemma.name())
            
            # Add antonyms if they exist
            if lemma.antonyms():
                for antonym in lemma.antonyms():
                    if antonym.name() not in antonyms:
                        antonyms.append(antonym.name())

    return synonyms, antonyms

# Test the function with the word "active"
word = "active"
synonyms, antonyms = get_synonyms_antonyms(word)

# Print results
print(f"Word: {word}")
print("\nSynonyms:")
for i, syn in enumerate(synonyms, 1):
    print(f"{i}. {syn}")

print("\nAntonyms:")
for i, ant in enumerate(antonyms, 1):
    print(f"{i}. {ant}")

# Additional information about synsets
print("\nDetailed Synset Information:")
for synset in wordnet.synsets(word):
    print(f"\nSynset: {synset.name()}")
    print(f"Definition: {synset.definition()}")
    print(f"Example sentences: {synset.examples()}")