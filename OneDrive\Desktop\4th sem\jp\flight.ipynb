pip install matplotlib cartopy


import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.feature as cfeature

# Waypoints: (lat, lon)
flight_path = [
    (2.7456, 101.7090),   # Kuala Lumpur
    (6.97, 103.63),       # IGARI (last radar contact)
    (5.29, 100.26),       # Penang (turnaround)
    (9.84, 92.54),        # Andaman Sea (military radar)
    (-34.5, 93.0),        # Estimated crash site (Inmarsat Arc)
]

labels = ['Kuala Lumpur', 'IGARI', 'Penang', 'Andaman Sea', 'Estimated Crash']

# Plot setup
plt.figure(figsize=(12, 8))
ax = plt.axes(projection=ccrs.PlateCarree())

ax.stock_img()
ax.coastlines()
ax.add_feature(cfeature.BORDERS, linestyle=':')
ax.set_extent((60, 120, -45, 30))  # India to Australia

# Plot flight path
lats, lons = zip(*flight_path)
ax.plot(lons, lats, marker='o', color='red', linewidth=2, markersize=6, label="MH370 Estimated Path")

# Annotate waypoints
for (lat, lon), label in zip(flight_path, labels):
    ax.text(lon + 1, lat + 1, label, fontsize=9, color='blue')

plt.title("Malaysia Airlines Flight MH370 - Estimated Path")
plt.legend()
plt.grid(True)
plt.show()


