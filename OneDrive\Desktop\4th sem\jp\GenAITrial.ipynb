{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["king - man + woman = [('queen', 0.7118193507194519), ('monarch', 0.6189674139022827), ('princess', 0.5902431011199951), ('crown_prince', 0.5499460697174072), ('prince', 0.5377321839332581), ('kings', 0.5236844420433044), ('Queen_Consort', 0.5235945582389832), ('queens', 0.5181134343147278), ('sultan', 0.5098593831062317), ('monarchy', 0.5087411999702454)] (Similarity: [('queen', 0.7118193507194519), ('monarch', 0.6189674139022827), ('princess', 0.5902431011199951), ('crown_prince', 0.5499460697174072), ('prince', 0.5377321839332581), ('kings', 0.5236844420433044), ('Queen_Consort', 0.5235945582389832), ('queens', 0.5181134343147278), ('sultan', 0.5098593831062317), ('monarchy', 0.5087411999702454)])\n", "\n", "\n", "Vector arithmetic: king - man + woman\n", "king: 0.8449392318725586\n", "queen: 0.7300517559051514\n", "monarch: 0.645466148853302\n", "princess: 0.6156251430511475\n", "crown_prince: 0.5818676352500916\n", "\n", "\n", "paris - france + germany = [('berlin', 0.48413652181625366), ('german', 0.4656967222690582), ('lindsay_lohan', 0.45592251420021057), ('heidi', 0.4484093487262726), ('switzerland', 0.44479838013648987), ('lil_kim', 0.44306042790412903), ('las_vegas', 0.4418063759803772), ('christina', 0.43938425183296204), ('joel', 0.4375365674495697), ('russia', 0.43744248151779175)] (Similarity: [('berlin', 0.48413652181625366), ('german', 0.4656967222690582), ('lindsay_lohan', 0.45592251420021057), ('heidi', 0.4484093487262726), ('switzerland', 0.44479838013648987), ('lil_kim', 0.44306042790412903), ('las_vegas', 0.4418063759803772), ('christina', 0.43938425183296204), ('joel', 0.4375365674495697), ('russia', 0.43744248151779175)])\n", "\n", "\n", "Vector arithmetic: paris - france + germany\n", "paris: 0.7316325902938843\n", "germany: 0.6952009797096252\n", "berlin: 0.48383620381355286\n", "german: 0.4694601595401764\n", "lindsay_lohan: 0.4535733461380005\n", "\n", "\n", "algorithm - code + python = [('monitor_lizard', 0.44948646426200867), ('croc', 0.43632182478904724), ('crocodile', 0.4357571303844452), ('alligator', 0.4347577393054962), ('reptile', 0.4292127192020416), ('snake', 0.4271015524864197), ('pythons', 0.425456702709198), ('boa_constrictor', 0.422867089509964), ('lizard', 0.42237091064453125), ('Burmese_python', 0.4222891926765442)] (Similarity: [('monitor_lizard', 0.44948646426200867), ('croc', 0.43632182478904724), ('crocodile', 0.4357571303844452), ('alligator', 0.4347577393054962), ('reptile', 0.4292127192020416), ('snake', 0.4271015524864197), ('pythons', 0.425456702709198), ('boa_constrictor', 0.422867089509964), ('lizard', 0.42237091064453125), ('Burmese_python', 0.4222891926765442)])\n", "\n", "\n", "Vector arithmetic: algorithm - code + python\n", "python: 0.703292191028595\n", "algorithm: 0.5404032468795776\n", "monitor_lizard: 0.4696113169193268\n", "croc: 0.4617742896080017\n", "crocodile: 0.46091994643211365\n"]}], "source": ["import gensim.downloader as api\n", "from gensim.models import KeyedVectors\n", "import numpy as np\n", "\n", "# Load pre-trained word vectors (this might take a while the first time)\n", "try:\n", "    wv = api.load('word2vec-google-news-300')  # Or another model like 'glove-wiki-gigaword-100'\n", "except ValueError:  # In case the model is not found, download it\n", "    print(\"Downloading the model, please wait...\")\n", "    wv = api.load('word2vec-google-news-300')\n", "\n", "# Example word relationships and vector arithmetic\n", "\n", "def print_word_relationships(word1, word2, word3):\n", "    try:\n", "        result = wv.most_similar(positive=[word1, word3], negative=[word2])\n", "        print(f\"{word1} - {word2} + {word3} = {result} (Similarity: {result})\")\n", "    except KeyError as e:\n", "        print(f\"One or more words not found in vocabulary: {e}\")\n", "        return\n", "\n", "def vector_arithmetic(word1, word2, word3):\n", "    try:\n", "        vec1 = wv[word1]\n", "        vec2 = wv[word2]\n", "        vec3 = wv[word3]\n", "\n", "        result_vector = vec1 - vec2 + vec3\n", "\n", "        # Find the most similar words to the resulting vector\n", "        similar_words = wv.most_similar(positive=[result_vector])\n", "        print(f\"Vector arithmetic: {word1} - {word2} + {word3}\")\n", "        for word, similarity in similar_words[:5]:  # Print top 5 similar words\n", "            print(f\"{word}: {similarity}\")\n", "\n", "    except KeyError as e:\n", "        print(f\"One or more words not found in vocabulary: {e}\")\n", "        return\n", "\n", "# Classic Example\n", "print_word_relationships(\"king\", \"man\", \"woman\")\n", "print(\"\\n\")\n", "vector_arithmetic(\"king\", \"man\", \"woman\")\n", "print(\"\\n\")\n", "\n", "# Another Example\n", "print_word_relationships(\"paris\", \"france\", \"germany\")\n", "print(\"\\n\")\n", "vector_arithmetic(\"paris\", \"france\", \"germany\")\n", "print(\"\\n\")\n", "\n", "# Example with words not in the vocabulary\n", "print_word_relationships(\"algorithm\", \"code\", \"python\")\n", "print(\"\\n\")\n", "vector_arithmetic(\"algorithm\", \"code\", \"python\")\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Semantically similar words to 'king':\n", "kings: 0.7138045430183411\n", "queen: 0.6510956883430481\n", "monarch: 0.6413194537162781\n", "crown_prince: 0.6204220056533813\n", "prince: 0.6159993410110474\n", "Semantically similar words to 'algorithm':\n", "algorithms: 0.8449932336807251\n", "mathematical_algorithm: 0.651436984539032\n", "algorithmically: 0.6436825394630432\n", "Algorithm: 0.6404630541801453\n", "algorithmic: 0.6347928047180176\n"]}], "source": ["import gensim.downloader as api\n", "from sklearn.decomposition import PCA\n", "from sklearn.manifold import TSNE\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# Load pre-trained word vectors (if not already loaded in the previous question)\n", "try:\n", "    wv = api.load('word2vec-google-news-300')\n", "except ValueError:\n", "    print(\"Downloading the model, please wait...\")\n", "    wv = api.load('word2vec-google-news-300')\n", "\n", "def visualize_word_embeddings(words, method='PCA'):\n", "    try:\n", "        vectors = [wv[word] for word in words]\n", "    except KeyError as e:\n", "        print(f\"One or more words not found in vocabulary: {e}\")\n", "        return\n", "\n", "    vectors = np.array(vectors)\n", "\n", "    if method == 'PCA':\n", "        reducer = PCA(n_components=2)\n", "        reduced_vectors = reducer.fit_transform(vectors)\n", "    elif method == 't-SNE':\n", "        reducer = TSNE(n_components=2,perplexity=1,random_state=42)\n", "        reduced_vectors = reducer.fit_transform(vectors)\n", "# Dimensionality reduction using PCA or t-SNE\n", "\n", "    else:\n", "        print(\"Invalid dimensionality reduction method. Choose 'PCA' or 't-SNE'.\")\n", "        return\n", "\n", "# Plotting the reduced vectors\n", "    plt.figure(figsize=(8, 6))\n", "    plt.scatter(reduced_vectors[:, 0], reduced_vectors[:, 1])\n", "\n", "    for i, word in enumerate(words):\n", "        plt.annotate(word, xy=(reduced_vectors[i, 0], reduced_vectors[i, 1]), textcoords=\"offset points\", xytext=(0, 10), ha='center')\n", "\n", "    plt.title(f\"Word Embedding Visualization ({method})\")\n", "    plt.xlabel(f\"{method} Dimension 1\")\n", "    plt.ylabel(f\"{method} Dimension 2\")\n", "    plt.grid(True)\n", "    plt.show()\n", "\n", "# Finding and printing semantically similar words\n", "def similar_words(word, topn=5):\n", "    try:\n", "        similar = wv.most_similar(word, topn=topn)\n", "        print(f\"Semantically similar words to '{word}':\")\n", "        for w, s in similar:\n", "            print(f\"{w}: {s}\")\n", "    except KeyError:\n", "        print(f\"Word '{word}' not found in vocabulary.\")\n", "# Example usage: Sports Domain\n", "sports_words = [\"football\", \"basketball\", \"tennis\", \"soccer\", \"baseball\", \"coach\", \"stadium\", \"athlete\", \"tournament\", \"championship\"]\n", "visualize_word_embeddings(sports_words, method='PCA')\n", "visualize_word_embeddings(sports_words, method='t-SNE')\n", "\n", "# Example usage: Technology Domain\n", "tech_words = [\"computer\", \"software\", \"hardware\", \"internet\", \"network\", \"programming\", \"algorithm\", \"database\", \"cloud\", \"artificial_intelligence\"]\n", "visualize_word_embeddings(tech_words, method='PCA')\n", "visualize_word_embeddings(tech_words, method='t-SNE')\n", "\n", "# Example of semantically similar words\n", "similar_words(\"king\")\n", "similar_words(\"algorithm\") # Word not in vocabulary\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Word: court\n", "Vector: [ 5.6193457e-03  5.5006146e-03  1.8298200e-03  5.7438342e-03\n", " -8.9781648e-03  6.5524476e-03  9.2250537e-03 -4.1972222e-03\n", "  1.6033029e-03 -5.2469359e-03  1.0547747e-03  2.7623291e-03\n", "  8.1623513e-03  5.4322486e-04  2.5622360e-03  1.2896319e-03\n", "  8.4145740e-03 -5.7120044e-03 -6.2644379e-03 -3.6289818e-03\n", " -2.3066103e-03  5.0415774e-03 -8.1214430e-03 -2.8265764e-03\n", " -8.2108751e-03  5.1536057e-03 -2.5773502e-03 -9.0686791e-03\n", "  4.0692114e-03  9.0153264e-03 -3.0349754e-03 -5.8434890e-03\n", "  3.0058781e-03 -4.4467786e-04 -9.9894954e-03  8.4270276e-03\n", " -7.3317448e-03 -4.9383915e-03 -2.6689384e-03 -5.4541943e-03\n", "  1.7211819e-03  9.7135175e-03  4.5848801e-03  8.0801789e-03\n", " -4.6704721e-04  6.3493918e-04 -2.6738476e-03 -8.7871458e-03\n", "  3.4299232e-03  2.0944914e-03 -9.4155809e-03 -4.9783480e-03\n", " -9.7317658e-03 -5.7235127e-03  4.0521766e-03  8.6399512e-03\n", "  4.1213916e-03  2.3856706e-03  8.1406478e-03 -1.1276166e-03\n", " -1.3897646e-03 -8.7524364e-03 -1.1614934e-04 -2.5625096e-03\n", "  3.8930262e-04  7.2882427e-03 -7.0449277e-03 -3.9427346e-03\n", " -6.6688587e-03 -3.5487576e-03 -3.3130241e-03  2.1411953e-03\n", "  3.3310356e-03 -4.9585025e-03 -4.5270091e-03  1.1448814e-03\n", "  5.4507391e-03  5.3831418e-03 -2.9727223e-03 -4.2648795e-03\n", " -5.6189224e-03 -5.4841716e-04  1.9469173e-03  1.5322529e-03\n", "  7.3488355e-03 -2.7385699e-03 -6.3614883e-05 -5.5172732e-03\n", " -1.1710254e-03 -7.7051553e-03 -9.5164246e-04  1.3125692e-03\n", " -8.5957693e-03  8.7417047e-03 -9.1954796e-03 -9.6158581e-03\n", " -8.5005676e-03  7.3112827e-03  5.4669138e-03  9.2464220e-03]\n", "Similar words: [('legal', 0.31904855370521545), ('judge', 0.1887820065021515), ('contract', 0.16208362579345703), ('guilty', 0.14660242199897766), ('after', 0.12774601578712463)]\n", "--------------------------------------------------------------------------------\n", "Word: judge\n", "Vector: [ 8.3534224e-03 -5.7011668e-04 -9.4315177e-03  4.7888681e-03\n", " -6.0535390e-03  6.6850143e-03  5.3692982e-03 -5.0432398e-03\n", "  2.5725325e-03  5.4188836e-03 -3.5801043e-03 -1.5085745e-03\n", "  9.1751488e-03  9.0642050e-03 -9.3998043e-03  7.5672497e-03\n", "  9.8942034e-03 -2.8441870e-03  2.4563496e-03 -2.8053403e-03\n", "  8.6406516e-03 -2.8824533e-04  5.6342692e-03  9.2131002e-03\n", "  4.1120690e-03 -7.1175187e-03 -1.9300373e-03  9.8017219e-04\n", "  2.0408235e-03  2.9581406e-03  9.4564715e-03  4.3971511e-03\n", "  9.9218879e-03 -8.6659072e-03 -5.7500293e-03  1.9783133e-03\n", "  3.6550935e-03 -1.0022422e-03 -6.9106333e-03 -3.2172471e-03\n", " -8.5317409e-03  9.4085895e-03  3.7280431e-03 -7.8849858e-03\n", "  3.1843402e-03  4.1644657e-03 -5.6362236e-03 -5.9178751e-03\n", "  1.0327493e-03  8.9640366e-03 -9.6427817e-03  9.9149338e-06\n", " -6.8735839e-03 -9.2734251e-04  3.0410651e-03 -5.0375704e-03\n", " -2.7748561e-03  6.7808456e-04 -6.3697482e-03  7.2862548e-03\n", "  4.3774773e-03 -8.5539082e-03 -2.1535184e-03  3.1731350e-03\n", " -8.3288401e-03 -7.0674210e-03 -8.4457286e-03 -5.4965173e-03\n", "  8.8501358e-03  7.0828679e-03  2.8882839e-03 -8.5539250e-03\n", "  5.7463273e-03  4.6289805e-03  1.2013063e-04 -8.8502504e-03\n", " -1.8850433e-03  1.9640379e-04 -7.7555021e-03  2.4766254e-03\n", "  4.8283156e-04 -7.0646331e-03 -8.2681132e-03  6.0480642e-03\n", " -8.3534149e-03 -5.5775866e-03  5.5830525e-03 -4.8568516e-04\n", " -3.0583569e-03 -5.1873415e-03 -1.1932332e-03  5.2647879e-03\n", " -5.9449319e-03 -4.9808170e-03 -4.9534808e-03 -4.7601508e-03\n", " -7.9818768e-03 -9.7500104e-03  7.5814626e-03  7.9980036e-03]\n", "Similar words: [('law', 0.3042301535606384), ('the', 0.19723187386989594), ('provided', 0.19516125321388245), ('court', 0.1887820065021515), ('verdict', 0.16703622043132782)]\n", "--------------------------------------------------------------------------------\n", "Word: lawyer\n", "Vector: [ 0.00256497  0.00084506 -0.0025324   0.00935375  0.00275953  0.00409102\n", " -0.00118765  0.00090786  0.006618   -0.00073168  0.00333738 -0.00067935\n", "  0.00524952  0.00364761  0.00259272 -0.00531075 -0.00469897  0.00430596\n", " -0.00590227 -0.00018137 -0.00062997  0.00348989 -0.008441    0.00880473\n", " -0.00145498 -0.00532522  0.0040497  -0.00193299 -0.00776337 -0.00449652\n", " -0.00038097 -0.0089485   0.00055676  0.00243525 -0.0032267   0.00256935\n", "  0.00248582  0.00999202  0.00142808  0.00202262  0.00278998 -0.00207894\n", " -0.00869186  0.00801753 -0.0019753  -0.00969499 -0.00655624 -0.00394866\n", "  0.00395616  0.00503763  0.00608981 -0.00677466  0.00069624 -0.0027719\n", " -0.00521555  0.00698459  0.0039599  -0.00309935 -0.0082789  -0.00514169\n", " -0.00065335  0.0078096   0.00604474 -0.00844421 -0.00956022  0.00714189\n", " -0.00232218 -0.00369748  0.00575251 -0.00583762  0.00509493 -0.00024243\n", " -0.00687442 -0.00033275  0.00636942  0.00929395  0.00222443  0.00505574\n", " -0.00497231 -0.00079717 -0.00532362  0.00118863 -0.00179271 -0.00363203\n", " -0.00701713  0.00965582  0.00297311 -0.00227762 -0.00418272  0.00772061\n", " -0.00647686  0.00312201  0.00078525  0.0083128   0.00683439 -0.00289829\n", "  0.0025426  -0.00166948 -0.00945237 -0.00260892]\n", "Similar words: [('lack', 0.2551238536834717), ('plaintiff', 0.18651695549488068), ('evidence.', 0.14852826297283173), ('prepared', 0.11184480041265488), ('agreement.', 0.10755585134029388)]\n", "--------------------------------------------------------------------------------\n", "Word: contract\n", "Vector: [ 7.6937964e-03  9.1199931e-03  1.1347448e-03 -8.3224503e-03\n", "  8.4219174e-03 -3.7034464e-03  5.7406356e-03  4.3989969e-03\n", "  9.6855229e-03 -9.2978226e-03  9.2093945e-03 -9.2885289e-03\n", " -6.9078384e-03 -9.0964586e-03 -5.5431020e-03  7.3667392e-03\n", "  9.1703814e-03 -3.3278819e-03  3.7209631e-03 -3.6277785e-03\n", "  7.8848880e-03  5.8668223e-03  1.4671482e-06 -3.6294849e-03\n", " -7.2249952e-03  4.7704377e-03  1.4475923e-03 -2.6077344e-03\n", "  7.8340750e-03 -4.0473263e-03 -9.1407774e-03 -2.2569739e-03\n", "  1.2745900e-04 -6.6460264e-03 -5.4900083e-03 -8.4955310e-03\n", "  9.2355907e-03  7.4244654e-03 -2.9905711e-04  7.3672007e-03\n", "  7.9516815e-03 -7.8339252e-04  6.6142259e-03  3.7643292e-03\n", "  5.0753648e-03  7.2543058e-03 -4.7396561e-03 -2.1847901e-03\n", "  8.7351468e-04  4.2401911e-03  3.3093067e-03  5.0909258e-03\n", "  4.5856843e-03 -8.4415618e-03 -3.1869307e-03 -7.2376858e-03\n", "  9.6859699e-03  5.0074058e-03  1.6779537e-04  4.1124816e-03\n", " -7.6558399e-03 -6.2926724e-03  3.0786775e-03  6.5358882e-03\n", "  3.9511123e-03  6.0235676e-03 -1.9818922e-03 -3.3419118e-03\n", "  2.0256442e-04 -3.1894220e-03 -5.5153845e-03 -7.7891261e-03\n", "  6.5389909e-03 -1.0908286e-03 -1.8820215e-03 -7.8070872e-03\n", "  9.3356231e-03  8.7212818e-04  1.7705472e-03  2.4907051e-03\n", " -7.3909219e-03  1.6368072e-03  2.9741626e-03 -8.5627520e-03\n", "  4.9519693e-03  2.4290767e-03  7.5011617e-03  5.0438843e-03\n", " -3.0289143e-03 -7.1585653e-03  7.1012718e-03  1.9021835e-03\n", "  5.2000978e-03  6.3784122e-03  1.9142431e-03 -6.1238203e-03\n", " -4.2070742e-06  8.2669621e-03 -6.0986802e-03  9.4395112e-03]\n", "Similar words: [('on', 0.26220637559890747), ('documents', 0.2467024326324463), ('found', 0.19602249562740326), ('specializes', 0.1898755431175232), ('guilty', 0.18396510183811188)]\n", "--------------------------------------------------------------------------------\n", "Word: verdict\n", "Vector: [-6.9629708e-03 -2.4567295e-03 -8.0231549e-03  7.5005428e-03\n", "  6.1253277e-03  5.2588633e-03  8.3763180e-03 -6.9689081e-04\n", " -9.3137082e-03  9.1162082e-03 -4.9302112e-03  7.8460053e-03\n", "  5.5363746e-03 -1.0781478e-03 -7.6654423e-03 -1.4635071e-03\n", "  6.2562455e-03 -6.9656502e-03  1.4418226e-03 -7.9518436e-03\n", "  8.7215779e-03 -2.8557647e-03  9.4375750e-03 -5.7093175e-03\n", " -9.7190049e-03 -8.6267618e-03 -4.0767137e-03  4.7099814e-03\n", " -2.4143749e-04  9.2235468e-03  3.1115937e-03  3.7455026e-03\n", "  2.9962619e-03  8.1457952e-03 -2.3976383e-03  7.4079144e-03\n", " -9.5346346e-03  2.9213501e-03 -6.8254530e-04  4.5020127e-04\n", "  6.8419981e-03 -2.8423427e-03 -2.3537970e-03 -1.0146087e-04\n", " -5.0006789e-04 -3.5775329e-03  6.2428946e-03 -6.5617231e-03\n", "  7.8910030e-03 -9.5364601e-05  2.6104774e-03  3.2216476e-03\n", " -2.8205366e-04  1.7058683e-03 -3.1411955e-03  4.7566053e-03\n", "  2.4374045e-04 -3.2812380e-03 -8.7183733e-03 -9.9987211e-03\n", "  3.1526745e-04 -5.7485383e-03 -1.1097634e-03 -4.2028395e-03\n", " -8.6391065e-03  1.0633849e-03  5.9115975e-03 -2.2113286e-03\n", " -7.1722935e-03  3.1568496e-03 -3.8649910e-04 -5.5225827e-03\n", " -1.1042289e-03 -6.4017554e-04 -3.1809709e-03 -9.9546947e-03\n", "  7.6382901e-03  3.7283804e-03 -2.5297292e-03  7.3079066e-03\n", "  4.5404537e-04  7.1727657e-03 -1.5491855e-03  7.4944529e-03\n", " -4.6171579e-05 -6.0780332e-03 -4.7164266e-03  9.6293185e-03\n", "  5.8059627e-04  1.0295908e-03  8.4513715e-03 -6.2896949e-03\n", " -1.7641417e-03 -8.1811417e-03 -6.6740769e-03 -8.5809249e-03\n", "  3.9293878e-03  2.7410304e-03  5.6138239e-03  2.5706294e-03]\n", "Similar words: [('for', 0.20100556313991547), ('law', 0.177714541554451), ('judge', 0.167036235332489), ('provided', 0.15773147344589233), ('based', 0.15575724840164185)]\n", "--------------------------------------------------------------------------------\n", "Word: plaintiff\n", "Vector: [ 2.0150025e-04  2.1564357e-03  1.0507379e-03  7.5847325e-03\n", " -4.4347146e-03  3.0858204e-04  6.9985273e-03  2.2626934e-03\n", "  4.2273141e-03  4.3023201e-03 -1.2725566e-03 -8.3407313e-03\n", "  3.0525185e-03 -8.8595524e-03 -2.6568403e-03 -9.4022872e-03\n", " -4.4520129e-04 -7.8651272e-03  7.4124783e-03 -4.9010483e-03\n", "  9.9317459e-03 -9.0153115e-03 -4.3030507e-03  1.9272735e-03\n", " -2.9947150e-03  1.4506219e-03 -3.6973462e-03 -8.8659656e-04\n", " -5.7348236e-03 -9.8133767e-03  6.3652177e-03  3.7513056e-04\n", "  6.5306076e-03 -7.4869124e-03  3.6584984e-03 -9.8877139e-03\n", "  7.6173660e-03  5.9292531e-03 -6.1239861e-03  8.2055097e-03\n", " -5.5934079e-03 -8.5529061e-03  1.5104997e-03  3.1753378e-03\n", " -8.1089083e-03 -8.9272810e-03  9.1697900e-03  1.6355077e-03\n", "  8.6689671e-04  2.4814110e-03 -3.4958103e-03  1.7283425e-03\n", "  1.9469964e-03  9.7977519e-03 -5.5951010e-03  2.2617104e-03\n", "  6.3166185e-03 -6.6858227e-03 -8.9496104e-03  8.6516021e-03\n", "  6.8432433e-03 -4.0192092e-03  5.7322541e-03  8.0601289e-04\n", " -5.0119488e-03 -1.3276985e-03 -4.8897644e-03  6.1304225e-03\n", "  1.9469191e-03 -8.2916077e-03 -1.5472593e-04 -2.6596661e-03\n", "  6.6212658e-03 -1.1517091e-03  7.6795868e-03  1.9411881e-03\n", " -3.8297069e-03  8.1662824e-03  2.9482406e-03 -3.9684530e-03\n", " -5.6085442e-03 -3.0935162e-03 -5.1502883e-03  3.4549148e-03\n", " -9.2860619e-03 -5.8321930e-03  3.3451898e-03  2.0275256e-04\n", "  5.9991567e-03 -3.5982563e-03  4.7710723e-06  2.4675988e-03\n", "  5.4605389e-03  1.5965247e-03  9.8157376e-03  3.7162853e-03\n", " -7.1872831e-03 -1.3993230e-03 -8.3749322e-03 -5.3288089e-03]\n", "Similar words: [('lawyer', 0.18651695549488068), ('prepared', 0.18220244348049164), ('jury', 0.1806788593530655), ('for', 0.17553144693374634), ('was', 0.16321682929992676)]\n", "--------------------------------------------------------------------------------\n", "Word: defendant\n", "Vector: [-6.4497180e-03  7.3408699e-03  6.1096628e-03 -4.9036434e-03\n", " -1.7672498e-03 -2.5419702e-03  3.1153164e-03 -4.1289255e-04\n", " -2.7871269e-03 -9.0611130e-03  6.4368662e-03 -9.6375123e-03\n", " -8.6501930e-03  1.4164730e-03  2.9876910e-03 -1.1774292e-05\n", "  3.9994123e-04  2.5870719e-03  1.7864220e-03  7.5404034e-03\n", " -3.4222074e-03 -7.2211269e-03 -7.8686289e-03  7.9102544e-03\n", "  1.9486343e-03 -5.8091292e-03  6.3674375e-03  8.4731467e-03\n", "  7.9248473e-03 -6.8171718e-03 -5.1056882e-03 -2.0215935e-03\n", " -6.6025099e-03  4.6957531e-03  5.6550978e-03 -5.9749410e-03\n", "  7.2266352e-03 -7.6529151e-03  6.1925733e-03 -4.6324385e-03\n", "  1.9562941e-03 -3.2807500e-03  3.4000683e-03  7.7071888e-03\n", " -1.4046400e-03 -4.9740043e-03 -8.5726567e-03  3.6354270e-03\n", "  4.2308769e-03  6.7101512e-03 -4.2805830e-03 -9.7589605e-03\n", "  6.8034716e-03 -4.5403559e-03  4.4273841e-03  8.0454340e-03\n", " -3.9225239e-03  1.6272322e-03 -3.6125185e-03 -6.6120476e-03\n", " -5.6738872e-03 -1.3351008e-04 -7.5079584e-03 -1.9846654e-03\n", " -2.4841523e-03  6.7427806e-03  1.3821548e-03  5.8598802e-03\n", "  3.7892263e-03 -3.4927912e-03 -5.0964854e-03  9.6482830e-03\n", "  7.0866388e-03 -9.0490077e-03  5.2801259e-03  8.1772776e-03\n", "  9.1796499e-03 -8.0336044e-03 -2.0240196e-03  1.0809975e-03\n", "  2.8951631e-03 -1.6232809e-03 -4.7309045e-03  6.5017538e-03\n", "  6.9171670e-03 -4.0074237e-04 -5.8186040e-03 -4.3669534e-03\n", " -5.1410715e-03  4.2721904e-03  5.2914274e-04 -2.3594610e-03\n", " -9.0735303e-03 -1.5110233e-03  6.4420598e-03  6.3384371e-03\n", "  4.2200210e-03 -2.8792582e-03  9.0353042e-03  6.0436298e-04]\n", "Similar words: [('lack', 0.21769997477531433), ('client.', 0.18434493243694305), ('by', 0.16776084899902344), ('the', 0.14028242230415344), ('careful', 0.13254931569099426)]\n", "--------------------------------------------------------------------------------\n", "Word: crime\n", "Vector: [-2.39309599e-03  7.54519179e-03  2.04433128e-03  2.90816277e-03\n", " -1.24475732e-03 -9.90538672e-03  1.39777502e-03  2.89483136e-03\n", " -9.81563795e-03 -3.90707888e-03 -2.34503555e-03 -5.07184537e-03\n", "  3.96544253e-03  2.11491250e-03  4.33985144e-03 -6.48399815e-03\n", " -5.99175692e-03  4.71561309e-03  6.77735498e-03  5.52320713e-03\n", "  4.80135018e-03 -7.24611105e-03  8.16335995e-03 -6.96730847e-03\n", " -7.66546279e-03  8.50680470e-03 -2.48072017e-03 -9.38228518e-03\n", " -3.08673014e-03  5.96401049e-03 -2.87764031e-03  4.93659358e-03\n", "  1.04945655e-04  4.05624835e-03  1.18168851e-03 -9.32258368e-03\n", " -3.64518794e-03  8.83989129e-03  1.54132291e-03 -5.89489331e-03\n", "  7.01685296e-03  7.45389843e-03  7.24968780e-03  1.21559779e-05\n", " -9.15112812e-03 -3.48493969e-03  5.13409497e-03 -6.50672847e-03\n", "  9.74318571e-03 -9.76629648e-03 -8.11995938e-03 -5.31282136e-03\n", "  6.01619110e-03 -7.67809153e-03  8.63682758e-03 -9.87366401e-03\n", "  3.50095308e-03  3.29635339e-03  4.73644614e-05  9.54601169e-03\n", "  5.11083193e-03  2.93403573e-04  4.81519150e-03  6.41125720e-03\n", "  3.00756656e-03  9.41415317e-03 -1.33178430e-03 -8.38358887e-03\n", " -4.47973097e-03 -1.48345518e-03 -7.26795942e-03 -1.56583695e-03\n", "  7.94195477e-03 -5.02520381e-03  5.11226105e-03  4.34691459e-03\n", " -6.98597054e-04  1.08264608e-03  5.73205389e-03 -6.64049247e-03\n", "  3.89007432e-03  8.91419966e-03 -3.51572246e-03 -7.60908565e-03\n", " -9.20740888e-03 -9.01197549e-03  3.87435313e-03  5.11276070e-03\n", "  7.04691140e-03 -1.44656945e-03  9.76864481e-04  3.05166957e-03\n", " -9.87612922e-03 -9.41923354e-03  4.59298166e-03 -8.39624833e-03\n", " -3.18118487e-03 -4.30870522e-03  5.91324689e-03 -3.44629609e-03]\n", "Similar words: [('documents', 0.19656729698181152), ('legal', 0.17834822833538055), ('proceedings', 0.16812410950660706), ('until', 0.16164757311344147), ('appeal', 0.14152395725250244)]\n", "--------------------------------------------------------------------------------\n", "Word: testimony\n", "Vector: [-0.00777354 -0.0067447  -0.00316932  0.00661751 -0.00084944  0.00885605\n", " -0.00229291 -0.00528453  0.00388659  0.00220941 -0.00028372 -0.00247787\n", " -0.00599708  0.00404857 -0.00760685 -0.00892072 -0.00913947  0.00837864\n", "  0.0039945   0.00707454  0.00991307 -0.00721625  0.00404     0.00281837\n", "  0.00615476 -0.0033805   0.00930967 -0.00579389  0.00702066  0.00695926\n", " -0.00036513  0.00505471  0.00666056  0.00152331  0.00736964  0.00242537\n", " -0.00150671  0.00739929  0.00468232 -0.00318097  0.00163745 -0.00014848\n", "  0.00497776 -0.00541712 -0.0042027  -0.00092523  0.0038226  -0.00658418\n", "  0.00783966 -0.00308196  0.00575088  0.00285145  0.00681205 -0.0044737\n", " -0.00318899  0.00304274 -0.00276138  0.00593036 -0.00597607  0.00258888\n", " -0.00071567 -0.00061405  0.00253045 -0.00217149 -0.00425028 -0.00852306\n", "  0.00249777  0.0053249  -0.00588646  0.00912517  0.00402394 -0.00519161\n", " -0.00129557  0.00392146 -0.00698881  0.00204125  0.00549421  0.00627346\n", "  0.0054321   0.00365979 -0.00276301 -0.00607462  0.00814737 -0.00728707\n", " -0.00162781 -0.00328408  0.00650148 -0.00626883  0.00340703  0.00204977\n", " -0.00406532 -0.00824743 -0.00804058  0.00349413 -0.00955111  0.00570967\n", " -0.00292816 -0.0091796  -0.00515205  0.00774553]\n", "Similar words: [('for', 0.19555474817752838), ('until', 0.13647012412548065), ('verdict', 0.12138231098651886), ('judge', 0.09572115540504456), ('both', 0.09416220337152481)]\n", "--------------------------------------------------------------------------------\n", "Word: appeal\n", "Vector: [-7.8395911e-05 -9.9217184e-03 -7.5779962e-03  8.5541056e-03\n", "  1.8584136e-03 -8.2470551e-03 -8.2224980e-04  5.4773726e-03\n", " -9.3491795e-03 -6.9018272e-03 -1.0379086e-03 -9.8049054e-03\n", "  4.8884284e-04  1.9750064e-03  8.5558416e-03  9.6677226e-04\n", "  1.3458530e-03  4.8540500e-03 -4.3028146e-03  7.1689901e-03\n", "  1.8398464e-04 -6.3572911e-04 -9.6984180e-03  5.2199559e-04\n", "  6.7959270e-03  1.7446125e-03 -8.1296256e-03 -3.3096005e-03\n", " -6.0662846e-03  6.0499362e-03 -6.3805641e-03  5.2376343e-03\n", " -5.2117584e-03 -6.8774973e-03 -7.6046642e-03  3.8742600e-03\n", " -8.7619219e-03 -5.7043307e-03 -7.0732506e-03 -4.3310737e-03\n", " -4.0378026e-03  1.4673821e-03  6.5022567e-03 -1.2235949e-03\n", " -5.5038016e-03  1.5700589e-03  5.6167888e-03 -5.2632745e-03\n", " -5.3437701e-03  5.2032298e-03  6.2915636e-03 -7.8042434e-03\n", "  8.1941253e-03 -9.8579312e-03 -7.4094068e-03 -5.8972263e-03\n", "  1.8583124e-03  3.6699032e-05  3.4332955e-03  7.0089959e-03\n", "  4.8161377e-03 -3.1991473e-03 -6.4213071e-03 -8.2272664e-03\n", " -4.8049793e-04  9.7916666e-03 -7.6410039e-03  3.6528448e-03\n", "  4.3357103e-03 -4.3004290e-03 -6.9619082e-03 -5.4648342e-03\n", " -5.0044521e-03 -7.0112897e-03  5.9967674e-03 -7.7925059e-03\n", " -2.9457458e-03  4.4145836e-03  1.7794182e-04 -7.6620951e-03\n", " -4.6970635e-03  3.5749602e-03  2.5057406e-03 -1.8980179e-03\n", " -7.2310719e-04  2.5155970e-03  1.4527483e-03 -2.6099132e-03\n", "  5.3638108e-03 -7.6253409e-03  6.4029242e-03 -9.9807139e-03\n", " -2.2422906e-03  6.6515715e-03 -3.5869745e-03  6.5435488e-03\n", "  5.4258751e-03 -7.4156988e-03 -2.3824969e-04 -4.4899811e-03]\n", "Similar words: [('legal', 0.19461774826049805), ('by', 0.19331850111484528), ('jury', 0.1920195072889328), ('corporate', 0.16992296278476715), ('crime', 0.14152395725250244)]\n", "--------------------------------------------------------------------------------\n", "Word: king\n", "Word 'king' not in vocabulary.\n", "Word: court\n", "Vector: [ 5.6193457e-03  5.5006146e-03  1.8298200e-03  5.7438342e-03\n", " -8.9781648e-03  6.5524476e-03  9.2250537e-03 -4.1972222e-03\n", "  1.6033029e-03 -5.2469359e-03  1.0547747e-03  2.7623291e-03\n", "  8.1623513e-03  5.4322486e-04  2.5622360e-03  1.2896319e-03\n", "  8.4145740e-03 -5.7120044e-03 -6.2644379e-03 -3.6289818e-03\n", " -2.3066103e-03  5.0415774e-03 -8.1214430e-03 -2.8265764e-03\n", " -8.2108751e-03  5.1536057e-03 -2.5773502e-03 -9.0686791e-03\n", "  4.0692114e-03  9.0153264e-03 -3.0349754e-03 -5.8434890e-03\n", "  3.0058781e-03 -4.4467786e-04 -9.9894954e-03  8.4270276e-03\n", " -7.3317448e-03 -4.9383915e-03 -2.6689384e-03 -5.4541943e-03\n", "  1.7211819e-03  9.7135175e-03  4.5848801e-03  8.0801789e-03\n", " -4.6704721e-04  6.3493918e-04 -2.6738476e-03 -8.7871458e-03\n", "  3.4299232e-03  2.0944914e-03 -9.4155809e-03 -4.9783480e-03\n", " -9.7317658e-03 -5.7235127e-03  4.0521766e-03  8.6399512e-03\n", "  4.1213916e-03  2.3856706e-03  8.1406478e-03 -1.1276166e-03\n", " -1.3897646e-03 -8.7524364e-03 -1.1614934e-04 -2.5625096e-03\n", "  3.8930262e-04  7.2882427e-03 -7.0449277e-03 -3.9427346e-03\n", " -6.6688587e-03 -3.5487576e-03 -3.3130241e-03  2.1411953e-03\n", "  3.3310356e-03 -4.9585025e-03 -4.5270091e-03  1.1448814e-03\n", "  5.4507391e-03  5.3831418e-03 -2.9727223e-03 -4.2648795e-03\n", " -5.6189224e-03 -5.4841716e-04  1.9469173e-03  1.5322529e-03\n", "  7.3488355e-03 -2.7385699e-03 -6.3614883e-05 -5.5172732e-03\n", " -1.1710254e-03 -7.7051553e-03 -9.5164246e-04  1.3125692e-03\n", " -8.5957693e-03  8.7417047e-03 -9.1954796e-03 -9.6158581e-03\n", " -8.5005676e-03  7.3112827e-03  5.4669138e-03  9.2464220e-03]\n", "Similar words: [('legal', 0.31904855370521545), ('judge', 0.1887820065021515), ('contract', 0.16208362579345703), ('guilty', 0.14660242199897766), ('after', 0.12774601578712463)]\n", "--------------------------------------------------------------------------------\n"]}], "source": ["import gensim\n", "from gensim.models import Word2Vec\n", "\n", "# Defining a sample domain-specific corpus (legal - simplified)\n", "legal_corpus = [\n", "    \"The court heard the arguments from both the plaintiff and the defendant.\",\n", "    \"The judge delivered the verdict after careful consideration of the evidence.\",\n", "    \"The lawyer prepared the legal documents for the client.\",\n", "    \"The contract was signed by all parties involved in the agreement.\",\n", "    \"The case was dismissed due to lack of sufficient evidence.\",\n", "    \"The attorney argued for the appeal based on procedural grounds.\",\n", "    \"The jury found the defendant guilty of the crime .\",\n", "    \"The witness provided testimony about the events of the incident.\",\n", "    \"The law firm specializes in corporate law and intellectual property.\",\n", "    \"The legal proceedings were adjourned until the next scheduled hearing.\"\n", "]\n", "\n", "# Tokenizing sentences (splitting them into words)\n", "tokenized_corpus = [sentence.lower().split() for sentence in legal_corpus]  # Lowercasing ensures consistency\n", "\n", "# Training the Word2Vec model\n", "model = Word2Vec(sentences=tokenized_corpus, vector_size=100, window=5, min_count=1, sg=0)  # Adjust parameters as needed\n", "\n", "# Function to analyze words\n", "def analyze_word(word):\n", "    try:\n", "        print(f\"Word: {word}\")\n", "        print(f\"Vector: {model.wv[word]}\")  # Print the vector (optional)\n", "        similar_words = model.wv.most_similar(word, topn=5)\n", "        print(f\"Similar words: {similar_words}\")\n", "        print(\"-\" * 80)\n", "    except KeyError:\n", "        print(f\"Word '{word}' not in vocabulary.\")\n", "\n", "# Analyzing domain-specific words\n", "analyze_word(\"court\")\n", "analyze_word(\"judge\")\n", "analyze_word(\"lawyer\")\n", "analyze_word(\"contract\")\n", "analyze_word(\"verdict\")\n", "analyze_word(\"plaintiff\")\n", "analyze_word(\"defendant\")\n", "analyze_word(\"crime\")\n", "analyze_word(\"testimony\")\n", "analyze_word(\"appeal\")\n", "\n", "# Testing a word outside the domain\n", "analyze_word(\"king\")\n", "\n", "# Saving the model\n", "model.save(\"legal_word2vec.model\")\n", "\n", "# Loading the model\n", "loaded_model = Word2Vec.load(\"legal_word2vec.model\")\n", "\n", "# Testing the loaded model\n", "analyze_word(\"court\")\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original Prompt:\n", "Describe the benefits of using renewable energy.\n", "\n", "Enriched Prompt:\n", "Describe the benefits of using renewable energy. another biomass use renewable_energy benfits used ofthe Renewable renewables Benefits beneifts describe Explain Q_Describe this in benefi_ts utilizing benefit Define Determine Using renewable_fuels that uses\n", "\n", "Original Response:\n", " Renewable energy is energy that is collected from renewable resources that are naturally replenished on a human timescale. Sunlight and wind, for example, are such sources that are constantly being replenished. Renewable energy sources are plentiful and all around us. \n", "\n", "Renewable energy is important for a number of reasons:\n", "\n", "1. Reducing greenhouse gas emissions: Burning fossil fuels like coal, oil, and gas is the largest contributor to climate change, accounting for over 75% of global greenhouse gas emissions and nearly 90% of all carbon dioxide emissions. Transitioning to renewable energy sources can help reduce our carbon footprint and mitigate the effects of climate change.\n", "\n", "2. Sustainable energy supply: Renewable energy sources like wind, solar, and hydropower provide plenty of electricity with minimal impact on the environment. They are replenished naturally, ensuring a sustainable and stable energy supply for the future.\n", "\n", "3. Energy independence: Investing in renewable energy sources can reduce reliance on fossil fuels and foreign oil, enhancing energy security and political stability.\n", "\n", "4. Job creation: The renewable energy sector is a rapidly growing industry that offers numerous job opportunities. Developing and maintaining renewable energy infrastructure creates jobs in research, manufacturing, installation, and more.\n", "\n", "5. Reducing air pollution: Traditional energy sources poll\n", "\n", "Enriched Response:\n", " Utilizing renewable energy offers a variety of benefits, including:\n", "\n", "1. Determined Benefit: Utilizing renewable energy helps to reduce dependence on finite resources such as fossil fuels, whose consumption also comes with dire consequences for the environment. This move reduces the inherent danger of depending on unsustainable sources for energy.\n", "\n", "2. Switching to renewable energy sources benefits you in the form of improved health outcomes. This is because renewable energy sources produce electricity without emitting harmful pollutants and greenhouse gases, contributing to better air quality and a reduced health impact. \n", "\n", "3. Job Creation: The renewable energy sector offers numerous employment opportunities due to the current and future importance of the industry. This trait is especially important in supporting local economies and facilitating sustainable development. Job creation is a direct benefit of investing in and developing renewable energy technologies.\n", "\n", "4. Diversification of Energy Sources: The energy supply is diversified by utilizing renewable energy beyond just fossil fuels. Relying on multiple energy sources can mitigate risks associated with fluctuations in the price of fossil fuels or disruptions in their supply. Also, diversifying the energy supply helps ensure a more stable and secure energy foundation. \n", "\n", "5. Long-Term Energy Solutions: The dominant advantage of renewable energy is its potential to provide long-term energy solutions. Unlike fossil fuels,\n"]}], "source": ["import gensim.downloader as api\n", "from langchain.llms import Cohere # Or any other LLM you prefer\n", "from langchain.prompts import PromptTemplate\n", "\n", "# Load pre-trained word vectors (if not already loaded)\n", "try:\n", "    wv = api.load('word2vec-google-news-300') # Or another model\n", "except ValueError:\n", "    print(\"Downloading the model, please wait...\")\n", "    wv = api.load('word2vec-google-news-300')\n", "\n", "# Initialize LLM (replace with your actual API key)\n", "cohere_api_key = \"lTAUzUcspcW2TSU1AvWGN36gHRZ7JgiZ91CQwE8B\" # Replace with your Cohere API key\n", "llm = Cohere(cohere_api_key=cohere_api_key)\n", "\n", "def enrich_prompt(prompt, topn=5):\n", "    try:\n", "        words = prompt.split() # Simple word splitting (can be improved)\n", "        similar_words = set() # Use a set to avoid duplicates\n", "        for word in words:\n", "            try:\n", "                for similar_word, similarity in wv.most_similar(word, topn=topn):\n", "                    similar_words.add(similar_word)\n", "            except KeyError:\n", "                pass # Ignore words not in vocabulary\n", "\n", "        enriched_prompt = prompt + \" \" + \" \".join(similar_words)\n", "        return enriched_prompt\n", "    except Exception as e:\n", "        print(f\"An error occurred: {e}\")\n", "        return prompt # Return original prompt if enrichment fails\n", "def generate_response(prompt):\n", "    try:\n", "        template = \"\"\"{prompt}\"\"\"\n", "        prompt_template = PromptTemplate(\n", "            input_variables=[\"prompt\"],\n", "            template=template,\n", "        )\n", "        final_prompt = prompt_template.format(prompt=prompt)\n", "        response = llm(final_prompt)\n", "        return response\n", "    except Exception as e:\n", "        print(f\"An error occurred during response generation: {e}\")\n", "        return \"Error generating response.\"\n", "\n", "# Example usage:\n", "original_prompt = \"Describe the benefits of using renewable energy.\"\n", "\n", "enriched_prompt = enrich_prompt(original_prompt)\n", "\n", "print(\"Original Prompt:\")\n", "print(original_prompt)\n", "print(\"\\nEnriched Prompt:\")\n", "print(enriched_prompt)\n", "print(\"\\nOriginal Response:\")\n", "original_response = generate_response(original_prompt)\n", "print(original_response)\n", "print(\"\\nEnriched Response:\")\n", "enriched_response = generate_response(enriched_prompt)\n", "print(enriched_response)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "Failed to import transformers.pipelines because of the following error (look up to see its traceback):\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\tensorflow\\python\\pywrap_tensorflow.py\", line 73, in <module>\n    from tensorflow.python._pywrap_tensorflow_internal import *\nImportError: DLL load failed while importing _pywrap_tensorflow_internal: A dynamic link library (DLL) initialization routine failed.\n\n\nFailed to load the native TensorFlow runtime.\nSee https://www.tensorflow.org/install/errors for some common causes and solutions.\nIf you need help, create an issue at https://github.com/tensorflow/tensorflow/issues and include the entire stack trace above this error message.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mImportError\u001b[0m                               Traceback (most recent call last)", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\tensorflow\\python\\pywrap_tensorflow.py:73\u001b[0m\n\u001b[0;32m     72\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 73\u001b[0m   \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpython\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_pywrap_tensorflow_internal\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;241m*\u001b[39m\n\u001b[0;32m     74\u001b[0m \u001b[38;5;66;03m# This try catch logic is because there is no bazel equivalent for py_extension.\u001b[39;00m\n\u001b[0;32m     75\u001b[0m \u001b[38;5;66;03m# Externally in opensource we must enable exceptions to load the shared object\u001b[39;00m\n\u001b[0;32m     76\u001b[0m \u001b[38;5;66;03m# by exposing the PyInit symbols with pybind. This error will only be\u001b[39;00m\n\u001b[0;32m     77\u001b[0m \u001b[38;5;66;03m# caught internally or if someone changes the name of the target _pywrap_tensorflow_internal.\u001b[39;00m\n\u001b[0;32m     78\u001b[0m \n\u001b[0;32m     79\u001b[0m \u001b[38;5;66;03m# This logic is used in other internal projects using py_extension.\u001b[39;00m\n", "\u001b[1;31mImportError\u001b[0m: DLL load failed while importing _pywrap_tensorflow_internal: A dynamic link library (DLL) initialization routine failed.", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mImportError\u001b[0m                               Traceback (most recent call last)", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\transformers\\utils\\import_utils.py:1967\u001b[0m, in \u001b[0;36m_LazyModule._get_module\u001b[1;34m(self, module_name)\u001b[0m\n\u001b[0;32m   1966\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 1967\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mimportlib\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mimport_module\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m.\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mmodule_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;18;43m__name__\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1968\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[1;32mC:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\importlib\\__init__.py:126\u001b[0m, in \u001b[0;36mimport_module\u001b[1;34m(name, package)\u001b[0m\n\u001b[0;32m    125\u001b[0m         level \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m--> 126\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_bootstrap\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_gcd_import\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m[\u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m:\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpackage\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1050\u001b[0m, in \u001b[0;36m_gcd_import\u001b[1;34m(name, package, level)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1027\u001b[0m, in \u001b[0;36m_find_and_load\u001b[1;34m(name, import_)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1006\u001b[0m, in \u001b[0;36m_find_and_load_unlocked\u001b[1;34m(name, import_)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:688\u001b[0m, in \u001b[0;36m_load_unlocked\u001b[1;34m(spec)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap_external>:883\u001b[0m, in \u001b[0;36mexec_module\u001b[1;34m(self, module)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:241\u001b[0m, in \u001b[0;36m_call_with_frames_removed\u001b[1;34m(f, *args, **kwds)\u001b[0m\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\transformers\\pipelines\\__init__.py:26\u001b[0m\n\u001b[0;32m     25\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfeature_extraction_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m PreTrainedFeatureExtractor\n\u001b[1;32m---> 26\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mimage_processing_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m BaseImageProcessor\n\u001b[0;32m     27\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodels\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mauto\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconfiguration_auto\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m AutoConfig\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\transformers\\image_processing_utils.py:22\u001b[0m\n\u001b[0;32m     21\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mimage_processing_base\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m BatchFeature, ImageProcessingMixin\n\u001b[1;32m---> 22\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mimage_transforms\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m center_crop, normalize, rescale\n\u001b[0;32m     23\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mimage_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ChannelDimension, get_image_size\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\transformers\\image_transforms.py:47\u001b[0m\n\u001b[0;32m     46\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_tf_available():\n\u001b[1;32m---> 47\u001b[0m     \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mtf\u001b[39;00m\n\u001b[0;32m     49\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_flax_available():\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\tensorflow\\__init__.py:40\u001b[0m\n\u001b[0;32m     39\u001b[0m \u001b[38;5;66;03m# Do not remove this line; See https://github.com/tensorflow/tensorflow/issues/42596\u001b[39;00m\n\u001b[1;32m---> 40\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpython\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m pywrap_tensorflow \u001b[38;5;28;01mas\u001b[39;00m _pywrap_tensorflow  \u001b[38;5;66;03m# pylint: disable=unused-import\u001b[39;00m\n\u001b[0;32m     41\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpyt<PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtools\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m module_util \u001b[38;5;28;01mas\u001b[39;00m _module_util\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\tensorflow\\python\\pywrap_tensorflow.py:88\u001b[0m\n\u001b[0;32m     87\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m:\n\u001b[1;32m---> 88\u001b[0m   \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m(\n\u001b[0;32m     89\u001b[0m       \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtraceback\u001b[38;5;241m.\u001b[39mformat_exc()\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m     90\u001b[0m       \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mFailed to load the native TensorFlow runtime.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m     91\u001b[0m       \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mSee https://www.tensorflow.org/install/errors \u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m     92\u001b[0m       \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfor some common causes and solutions.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m     93\u001b[0m       \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mIf you need help, create an issue \u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m     94\u001b[0m       \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mat https://github.com/tensorflow/tensorflow/issues \u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m     95\u001b[0m       \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mand include the entire stack trace above this error message.\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     97\u001b[0m \u001b[38;5;66;03m# pylint: enable=wildcard-import,g-import-not-at-top,unused-import,line-too-long\u001b[39;00m\n", "\u001b[1;31mImportError\u001b[0m: Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\tensorflow\\python\\pywrap_tensorflow.py\", line 73, in <module>\n    from tensorflow.python._pywrap_tensorflow_internal import *\nImportError: DLL load failed while importing _pywrap_tensorflow_internal: A dynamic link library (DLL) initialization routine failed.\n\n\nFailed to load the native TensorFlow runtime.\nSee https://www.tensorflow.org/install/errors for some common causes and solutions.\nIf you need help, create an issue at https://github.com/tensorflow/tensorflow/issues and include the entire stack trace above this error message.", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtransformers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m pipeline\n\u001b[0;32m      3\u001b[0m \u001b[38;5;66;03m# <PERSON><PERSON> the sentiment analysis pipeline\u001b[39;00m\n\u001b[0;32m      4\u001b[0m classifier \u001b[38;5;241m=\u001b[39m pipeline(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msentiment-analysis\u001b[39m\u001b[38;5;124m\"\u001b[39m, model\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdistilbert-base-uncased-finetuned-sst-2-english\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1075\u001b[0m, in \u001b[0;36m_handle_fromlist\u001b[1;34m(module, fromlist, import_, recursive)\u001b[0m\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\transformers\\utils\\import_utils.py:1955\u001b[0m, in \u001b[0;36m_LazyModule.__getattr__\u001b[1;34m(self, name)\u001b[0m\n\u001b[0;32m   1953\u001b[0m     value \u001b[38;5;241m=\u001b[39m Placeholder\n\u001b[0;32m   1954\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_class_to_module\u001b[38;5;241m.\u001b[39mkeys():\n\u001b[1;32m-> 1955\u001b[0m     module \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_module\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_class_to_module\u001b[49m\u001b[43m[\u001b[49m\u001b[43mname\u001b[49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1956\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(module, name)\n\u001b[0;32m   1957\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_modules:\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\transformers\\utils\\import_utils.py:1969\u001b[0m, in \u001b[0;36m_LazyModule._get_module\u001b[1;34m(self, module_name)\u001b[0m\n\u001b[0;32m   1967\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m importlib\u001b[38;5;241m.\u001b[39mimport_module(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m+\u001b[39m module_name, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m)\n\u001b[0;32m   1968\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m-> 1969\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[0;32m   1970\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFailed to import \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmodule_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m because of the following error (look up to see its\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1971\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m traceback):\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1972\u001b[0m     ) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01me\u001b[39;00m\n", "\u001b[1;31mRuntimeError\u001b[0m: Failed to import transformers.pipelines because of the following error (look up to see its traceback):\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\tensorflow\\python\\pywrap_tensorflow.py\", line 73, in <module>\n    from tensorflow.python._pywrap_tensorflow_internal import *\nImportError: DLL load failed while importing _pywrap_tensorflow_internal: A dynamic link library (DLL) initialization routine failed.\n\n\nFailed to load the native TensorFlow runtime.\nSee https://www.tensorflow.org/install/errors for some common causes and solutions.\nIf you need help, create an issue at https://github.com/tensorflow/tensorflow/issues and include the entire stack trace above this error message."]}], "source": ["from transformers import pipeline\n", "\n", "# Load the sentiment analysis pipeline\n", "classifier = pipeline(\"sentiment-analysis\", model=\"distilbert-base-uncased-finetuned-sst-2-english\")\n", "\n", "def analyze_sentiment(sentences):\n", "    results = classifier(sentences)\n", "    return results\n", "\n", "# Example usage (Real-world application: Customer feedback analysis)\n", "customer_reviews = [\n", "    \"The product is amazing! I love it.\",\n", "    \"The service was terrible. I'm very disappointed.\",\n", "    \"It's okay. Nothing special.\",\n", "    \"The quality is excellent, but the price is a bit high.\",\n", "    \"I'm not sure about this product. I need to try it more.\"\n", "]\n", "\n", "sentiment_results = analyze_sentiment(customer_reviews)\n", "\n", "for i, review in enumerate(customer_reviews):\n", "    print(f\"Review: {review}\")\n", "    print(f\"Sentiment: {sentiment_results[i]['label']}\")\n", "    print(f\"Score: {sentiment_results[i]['score']:.4f}\")  # Format score to 4 decimal places\n", "    print(\"-\" * 20)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting transformers\n", "  Downloading transformers-4.51.3-py3-none-any.whl.metadata (38 kB)\n", "Requirement already satisfied: filelock in c:\\users\\<USER>\\appdata\\local\\packages\\pythonsoftwarefoundation.python.3.10_qbz5n2kfra8p0\\localcache\\local-packages\\python310\\site-packages (from transformers) (3.17.0)\n", "Collecting huggingface-hub<1.0,>=0.30.0 (from transformers)\n", "  Downloading huggingface_hub-0.30.2-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: numpy>=1.17 in c:\\users\\<USER>\\appdata\\local\\packages\\pythonsoftwarefoundation.python.3.10_qbz5n2kfra8p0\\localcache\\local-packages\\python310\\site-packages (from transformers) (1.26.4)\n", "Requirement already satisfied: packaging>=20.0 in c:\\users\\<USER>\\appdata\\local\\packages\\pythonsoftwarefoundation.python.3.10_qbz5n2kfra8p0\\localcache\\local-packages\\python310\\site-packages (from transformers) (24.2)\n", "Requirement already satisfied: pyyaml>=5.1 in c:\\users\\<USER>\\appdata\\local\\packages\\pythonsoftwarefoundation.python.3.10_qbz5n2kfra8p0\\localcache\\local-packages\\python310\\site-packages (from transformers) (6.0.2)\n", "Requirement already satisfied: regex!=2019.12.17 in c:\\users\\<USER>\\appdata\\local\\packages\\pythonsoftwarefoundation.python.3.10_qbz5n2kfra8p0\\localcache\\local-packages\\python310\\site-packages (from transformers) (2024.11.6)\n", "Requirement already satisfied: requests in c:\\users\\<USER>\\appdata\\local\\packages\\pythonsoftwarefoundation.python.3.10_qbz5n2kfra8p0\\localcache\\local-packages\\python310\\site-packages (from transformers) (2.32.3)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in c:\\users\\<USER>\\appdata\\local\\packages\\pythonsoftwarefoundation.python.3.10_qbz5n2kfra8p0\\localcache\\local-packages\\python310\\site-packages (from transformers) (0.21.1)\n", "Collecting safetensors>=0.4.3 (from transformers)\n", "  Downloading safetensors-0.5.3-cp38-abi3-win_amd64.whl.metadata (3.9 kB)\n", "Requirement already satisfied: tqdm>=4.27 in c:\\users\\<USER>\\appdata\\local\\packages\\pythonsoftwarefoundation.python.3.10_qbz5n2kfra8p0\\localcache\\local-packages\\python310\\site-packages (from transformers) (4.67.1)\n", "Requirement already satisfied: fsspec>=2023.5.0 in c:\\users\\<USER>\\appdata\\local\\packages\\pythonsoftwarefoundation.python.3.10_qbz5n2kfra8p0\\localcache\\local-packages\\python310\\site-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (2025.3.0)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in c:\\users\\<USER>\\appdata\\local\\packages\\pythonsoftwarefoundation.python.3.10_qbz5n2kfra8p0\\localcache\\local-packages\\python310\\site-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (4.12.2)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\appdata\\local\\packages\\pythonsoftwarefoundation.python.3.10_qbz5n2kfra8p0\\localcache\\local-packages\\python310\\site-packages (from tqdm>=4.27->transformers) (0.4.6)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\packages\\pythonsoftwarefoundation.python.3.10_qbz5n2kfra8p0\\localcache\\local-packages\\python310\\site-packages (from requests->transformers) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\appdata\\local\\packages\\pythonsoftwarefoundation.python.3.10_qbz5n2kfra8p0\\localcache\\local-packages\\python310\\site-packages (from requests->transformers) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\local\\packages\\pythonsoftwarefoundation.python.3.10_qbz5n2kfra8p0\\localcache\\local-packages\\python310\\site-packages (from requests->transformers) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\appdata\\local\\packages\\pythonsoftwarefoundation.python.3.10_qbz5n2kfra8p0\\localcache\\local-packages\\python310\\site-packages (from requests->transformers) (2025.1.31)\n", "Downloading transformers-4.51.3-py3-none-any.whl (10.4 MB)\n", "   ---------------------------------------- 0.0/10.4 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/10.4 MB ? eta -:--:--\n", "   --- ------------------------------------ 0.8/10.4 MB 2.0 MB/s eta 0:00:05\n", "   ------ --------------------------------- 1.6/10.4 MB 2.7 MB/s eta 0:00:04\n", "   --------- ------------------------------ 2.4/10.4 MB 3.2 MB/s eta 0:00:03\n", "   -------------- ------------------------- 3.7/10.4 MB 3.9 MB/s eta 0:00:02\n", "   ---------------------- ----------------- 5.8/10.4 MB 5.0 MB/s eta 0:00:01\n", "   ------------------------- -------------- 6.6/10.4 MB 5.0 MB/s eta 0:00:01\n", "   ---------------------------------- ----- 8.9/10.4 MB 5.7 MB/s eta 0:00:01\n", "   ---------------------------------------- 10.4/10.4 MB 6.2 MB/s eta 0:00:00\n", "Downloading huggingface_hub-0.30.2-py3-none-any.whl (481 kB)\n", "Downloading safetensors-0.5.3-cp38-abi3-win_amd64.whl (308 kB)\n", "Installing collected packages: safetensors, huggingface-hub, transformers\n", "  Attempting uninstall: huggingface-hub\n", "    Found existing installation: huggingface-hub 0.29.3\n", "    Uninstalling huggingface-hub-0.29.3:\n", "      Successfully uninstalled huggingface-hub-0.29.3\n", "Successfully installed huggingface-hub-0.30.2 safetensors-0.5.3 transformers-4.51.3\n", "Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 25.0.1 -> 25.1\n", "[notice] To update, run: C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\python.exe -m pip install --upgrade pip\n"]}], "source": ["pip install transformers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'googleapiclient'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 4\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mllms\u001b[39;00m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m <PERSON>here\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON>cha<PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mprompts\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m PromptTemplate\n\u001b[1;32m----> 4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mgoogleapiclient\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdiscovery\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m build\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01mgo<PERSON><PERSON>_auth_oauthlib\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mflow\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m InstalledAppFlow\n\u001b[0;32m      6\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mgoogle\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mauth\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtransport\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mrequests\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Request\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'googleapiclient'"]}], "source": ["import os\n", "from langchain.llms import Cohere\n", "from langchain.prompts import PromptTemplate\n", "from googleapiclient.discovery import build\n", "from google_auth_oauthlib.flow import InstalledAppFlow\n", "from google.auth.transport.requests import Request\n", "from google.oauth2.credentials import Credentials\n", "from googleapiclient.http import MediaIoBaseDownload\n", "import io\n", "\n", "# 1. Install Libraries (if not already installed):\n", "# pip install langchain cohere langchain-community google-api-python-client google-auth-httplib2 google-auth-oauthlib\n", "\n", "# 2. Cohere API Key:\n", "cohere_api_key = os.environ.get(\"COHERE_API_KEY\")  # Get API Key from environment variables\n", "if not cohere_api_key:\n", "    raise ValueError(\"Please set the COHERE_API_KEY environment variable.\")\n", "\n", "# 3. Google Drive Authentication:\n", "SCOPES = ['https://www.googleapis.com/auth/drive.readonly']\n", "CREDENTIALS_FILE = 'maximal-radius-458204-v3-37f8577faf63.json'  # Path to your credentials.json\n", "\n", "def authenticate_google_drive():\n", "    creds = None\n", "    if os.path.exists('token.json'):\n", "        creds = Credentials.from_authorized_user_file('token.json', SCOPES)\n", "    if not creds or not creds.valid:\n", "        if creds and creds.expired and creds.refresh_token:\n", "            creds.refresh(Request())\n", "        else:\n", "            flow = InstalledAppFlow.from_client_secrets_file(CREDENTIALS_FILE, SCOPES)\n", "            creds = flow.run_local_server(port=0, authorization_url_message=lambda url: print(f\"Please visit this URL to authorize the application: {url}\"))\n", "        with open('token.json', 'w') as token:\n", "            token.write(creds.to_json())\n", "    return build('drive', 'v3', credentials=creds)\n", "\n", "drive_service = authenticate_google_drive()\n", "\n", "# 4. Load Text Document from Google Drive:\n", "def load_document_from_drive(drive_service, file_id):\n", "    try:\n", "        file = drive_service.files().get(fileId=file_id).execute()\n", "\n", "        if file['mimeType'] == 'application/vnd.google-apps.document':\n", "            request = drive_service.files().export_media(fileId=file_id, mimeType='text/plain')\n", "        elif file['mimeType'] == 'text/plain':\n", "            request = drive_service.files().get_media(fileId=file_id)\n", "        else:\n", "            print(f\"File is of type: {file['mimeType']}. Only text/plain and application/vnd.google-apps.document files are supported\")\n", "            return None\n", "\n", "        fh = io.BytesIO()\n", "        downloader = MediaIoBaseDownload(fh, request)\n", "        done = False\n", "        while not done:\n", "            status, done = downloader.next_chunk()\n", "            print(f\"Download {int(status.progress() * 100)}%.\")\n", "        file_content = fh.getvalue().decode('utf-8')\n", "        return file_content\n", "\n", "    except Exception as e:\n", "        print(f\"An error occurred: {e}\")\n", "        return None\n", "\n", "# Replace with your actual Google Drive file ID\n", "file_id = 'YOUR_GOOGLE_DRIVE_FILE_ID'\n", "document_content = load_document_from_drive(drive_service, file_id)\n", "\n", "if document_content:\n", "    print(\"Document loaded successfully.\")\n", "else:\n", "    print(\"Failed to load document.\")\n", "    exit()\n", "\n", "# 5. Lang<PERSON><PERSON>n and Cohere Integration:\n", "llm = Cohere(cohere_api_key=cohere_api_key)\n", "\n", "# 6. Prompt Template:\n", "template = \"\"\"\n", "You are a helpful assistant. Please summarize the following document, highlighting the key points and providing a concise overview.\n", "\n", "Document:\n", "{document}\n", "\n", "Summary:\n", "---\n", "\"\"\"\n", "\n", "prompt = PromptTemplate(\n", "    input_variables=[\"document\"],\n", "    template=template,\n", ")\n", "\n", "final_prompt = prompt.format(document=document_content)\n", "output = llm(final_prompt)\n", "print(output)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n", "Download 100%.\n", "Document loaded successfully.\n", "Summary:\n", "<PERSON>, the protagonist of <PERSON> Leveling, starts as the \"World’s Weakest Hunter\" but gains a unique leveling system after surviving a deadly Double Dungeon. This system allows him to grow stronger rapidly, soloing dungeons and using abilities like Shadow Extraction to create a shadow army. Driven by a desire to protect his family, <PERSON><PERSON><PERSON> transforms into humanity’s strongest hunter, battling Monarchs and Rulers in a cosmic war. Ultimately, he rewrites fate, showcasing strength through perseverance."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "The University of Cambridge\n", "Founder:  Scholars who left the University of Oxford following a dispute\n", "Founded: 1209\n", "Branches: Cambridge, England\n", "Employees: Over 150 academic departments, faculties, and other institutions\n", "Summary: The University of Cambridge is a public collegiate research university and is the third-oldest operating university in the world.\n"]}], "source": ["import wikipedia\n", "from typing import List, Optional\n", "from pydantic import BaseModel, Field\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import PromptTemplate\n", "from langchain.llms import Cohere\n", "from langchain.chains import LLMChain\n", "class InstitutionInfo(BaseModel):\n", "    name: str = <PERSON>(description=\"Full name\")\n", "    founder: str = <PERSON>(description=\"Founder(s)\")\n", "    founding_year: str = Field(description=\"Founding year\")\n", "    branches: List[str] = Field(description=\"Locations/branches\")\n", "    employee_count: Optional[str] = Field(description=\"Employee count\")\n", "    summary: str = Field(description=\"Brief summary\")\n", "parser = PydanticOutputParser(pydantic_object=InstitutionInfo)\n", "template = \"\"\"Extract information about {institution_name} from: {context}\n", "{format_instructions}\"\"\"\n", "prompt = PromptTemplate(template=template, input_variables=[\"institution_name\", \"context\"], \n", "                       partial_variables={\"format_instructions\": parser.get_format_instructions()})\n", "def extract_info(institution_name, api_key):\n", "    try:\n", "        page = wikipedia.page(wikipedia.search(institution_name)[0])\n", "        context = page.content[:5000]\n", "        llm = Cohere(cohere_api_key=api_key)\n", "        chain = LLMChain(llm=llm, prompt=prompt)\n", "        output = chain.run(institution_name=institution_name, context=context)\n", "        return parser.parse(output)\n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        return None\n", "api_key = \"lTAUzUcspcW2TSU1AvWGN36gHRZ7JgiZ91CQwE8B\"\n", "institution = input(\"Institution name: \")\n", "info = extract_info(institution, api_key)\n", "if info:\n", "    print(f\"\\n{info.name}\\nFounder: {info.founder}\\nFounded: {info.founding_year}\")\n", "    print(f\"Branches: {', '.join(info.branches)}\\nEmployees: {info.employee_count}\")\n", "    print(f\"Summary: {info.summary}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Founder: Oxford scholars\n", "Founded: 1209\n", "Branches: 31 semi-autonomous constituent colleges\n", "Employees: 100 million learners (reached by Cambridge University Press and Assessment)\n", "Summary: The University of Cambridge is a public research university established in 1209 by Oxford scholars who moved to Cambridge following a dispute. It's the third-oldest continuously operating university worldwide and has 31 colleges. With a rich academic history, it's associated with 124 Nobel Prize winners and many notable alumni. Cambridge's diverse operations include 116 libraries, 8 museums, and a large press and assessment department.\n"]}], "source": ["import cohere,wikipedia,re\n", "from pydantic import BaseModel\n", "class InstitutionInfo(BaseModel):\n", " founder:str\n", " founded:str\n", " branches:str\n", " employees:str\n", " summary:str\n", "co=cohere.Client(\"lTAUzUcspcW2TSU1AvWGN36gHRZ7JgiZ91CQwE8B\")\n", "name=input(\"Enter institution name: \")\n", "title=wikipedia.search(name)[0]\n", "content=wikipedia.page(title).content[:3000]\n", "messages=[{\"role\":\"user\",\"content\":f\"\"\"Extract the following details from this Wikipedia content:\\n1. Founder\\n2. Founded (year)\\n3. Branches\\n4. Employees\\n5. 4-line Summary\\n\\nContent:\\n{content}\\n\\nReply in this format:\\nFounder:<>\\nFounded:<>\\nBranches:<>\\nEmployees:<>\\nSummary:<>\"\"\"}]\n", "res=co.chat(model=\"command-r\",message=messages[0][\"content\"])\n", "text=res.text\n", "print(text)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["05-05-2025 11:08:28 AM - time_profiler - INFO - get_wikipedia_title function ran in 0.00 secs\n", "05-05-2025 11:08:30 AM - time_profiler - INFO - get_wikipedia_content function ran in 1.82 secs\n", "05-05-2025 11:08:33 AM - time_profiler - INFO - get_cohere_response function ran in 2.38 secs\n", "05-05-2025 11:08:33 AM - time_profiler - INFO - print_result function ran in 0.00 secs\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Extracted Information:\n", "Founder: Unknown; attributed to <PERSON> the Great in the 14th century (apocryphal)\n", "Founded: 1096 (evidence of teaching); university status likely established later\n", "Branches: 43 constituent colleges, including 36 semi-autonomous colleges, four permanent private halls, and three societies\n", "Employees: Information not available\n", "Summary:\n", "The University of Oxford, located in England, has a long history dating back to the 11th century. It's the oldest university in the English-speaking world and one of the world's oldest continuously operating institutions. With 43 constituent colleges, it has a unique structure where each college is semi-autonomous. The university's foundation date is unclear, but its rich history includes notable alumni and contributions to academia. Oxford's reputation for excellence is enhanced by its Rhodes Scholars, Nobel Laureates, and world-leading research. Scatter across the city, the university has a strong alumni base and a consolidated income of £3.05 billion.\n"]}], "source": ["import cohere, wikipedia, re\n", "from pydantic import BaseModel\n", "from time_profiler import timer\n", "class InstitutionInfo(BaseModel):\n", "    founder: str\n", "    founded: str\n", "    branches: str\n", "    employees: str\n", "    summary: str\n", "@timer()\n", "def get_wikipedia_title(name: str) -> str:\n", "    return wikipedia.search(name)[0]\n", "@timer()\n", "def get_wikipedia_content(title: str) -> str:\n", "    return wikipedia.page(title).content[:3000]\n", "@timer()\n", "def get_cohere_response(content: str) -> str:\n", "    co = cohere.Client(\"lTAUzUcspcW2TSU1AvWGN36gHRZ7JgiZ91CQwE8B\")\n", "    prompt = f\"\"\"Extract details from Wikipedia content: 1. Founder 2. Founded (year) 3. Branches 4. Employees 5. 4-line Summary \n", "Content:\\n{content} \n", "Reply format: Founder:<> Founded:<> Branches:<> Employees:<> Summary:<>\"\"\"\n", "    return co.chat(model=\"command-r\", message=prompt).text\n", "@timer()\n", "def print_result(text: str):\n", "    print(\"\\nExtracted Information:\\n\" + text)\n", "def get_institution_info(name: str):\n", "    title = get_wikipedia_title(name)\n", "    content = get_wikipedia_content(title)\n", "    result = get_cohere_response(content)\n", "    print_result(result)\n", "if __name__ == \"__main__\":\n", "    name = input(\"Enter institution name: \")\n", "    get_institution_info(name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}