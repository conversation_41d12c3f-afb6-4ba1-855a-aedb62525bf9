{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Program 1"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package stopwords to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Tokenized words : ['NLP', 'is', 'a', 'fascinating', 'field', 'of', 'study', '.', 'It', 'involves', 'tasks', 'like', 'tokenization', ',', 'filtration', ',', 'stemming', ',', 'stop', 'word', 'removal', 'and', 'script', 'validation', '.']\n", "Filtered words : ['NLP', 'is', 'a', 'fascinating', 'field', 'of', 'study', 'It', 'involves', 'tasks', 'like', 'tokenization', 'filtration', 'stemming', 'stop', 'word', 'removal', 'and', 'script', 'validation']\n", "validated words : ['NLP', 'is', 'a', 'fascinating', 'field', 'of', 'study', 'It', 'involves', 'tasks', 'like', 'tokenization', 'filtration', 'stemming', 'stop', 'word', 'removal', 'and', 'script', 'validation']\n", "Tokens without stopwrods : ['NLP', 'fascinating', 'field', 'study', '.', 'It', 'involves', 'tasks', 'like', 'tokenization', ',', 'filtration', ',', 'stemming', ',', 'stop', 'word', 'removal', 'script', 'validation', '.']\n", "Stemmed words : ['nlp', 'is', 'a', 'fascin', 'field', 'of', 'studi', '.', 'it', 'involv', 'task', 'like', 'token', ',', 'filtrat', ',', 'stem', ',', 'stop', 'word', 'remov', 'and', 'script', 'valid', '.']\n", "Pre-processed text : nlp fascin field studi . it involv task like token , filtrat , stem , stop word remov script valid .\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[nltk_data]   Unzipping corpora\\stopwords.zip.\n"]}], "source": ["import nltk, re\n", "from nltk.corpus import stopwords\n", "from nltk.tokenize import word_tokenize\n", "from nltk.stem import PorterStemmer\n", "\n", "nltk.download('punkt')\n", "nltk.download('stopwords')\n", "\n", "text = \"NLP is a fascinating field of study. It involves tasks like tokenization, filtration, stemming, stop word removal and script validation.\"\n", "print(\"Tokenized words :\", word_tokenize(text))\n", "print(\"Filtered words :\", [word for word in word_tokenize(text) if word.isalnum()])\n", "print(\"validated words :\", [word for word in word_tokenize(text) if re.match(\"^[a-zA-Z0-9_]*$\", word)])\n", "print(\"Tokens without stopwrods :\", [word for word in word_tokenize(text) if word not in stopwords.words('english')])\n", "print(\"Stemmed words :\", [<PERSON><PERSON><PERSON><PERSON>().stem(word) for word in word_tokenize(text)])\n", "print(\"Pre-processed text :\", \" \".join([PorterStemmer().stem(word) for word in word_tokenize(text) if word not in stopwords.words('english')]))\n", "\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Program 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tokenized sentences: \n", " [['i', 'love', 'natural', 'language', 'processing'], ['programming', 'is', 'fun', 'and', 'rewarding'], ['i', 'love', 'programming']]\n", "Unigrams: \n", " [('i',), ('love',), ('natural',), ('language',), ('processing',), ('programming',), ('is',), ('fun',), ('and',), ('rewarding',), ('i',), ('love',), ('programming',)] \n", "Bigrams: \n", " [('i', 'love'), ('love', 'natural'), ('natural', 'language'), ('language', 'processing'), ('programming', 'is'), ('is', 'fun'), ('fun', 'and'), ('and', 'rewarding'), ('i', 'love'), ('love', 'programming')] \n", "Trigrams: \n", " [('i', 'love', 'natural'), ('love', 'natural', 'language'), ('natural', 'language', 'processing'), ('programming', 'is', 'fun'), ('is', 'fun', 'and'), ('fun', 'and', 'rewarding'), ('i', 'love', 'programming')]\n", "Unigram Prob: \n", "\n", "('i',) : 0.1538\n", "('love',) : 0.1538\n", "('natural',) : 0.0769\n", "('language',) : 0.0769\n", "('processing',) : 0.0769\n", "('programming',) : 0.1538\n", "('is',) : 0.0769\n", "('fun',) : 0.0769\n", "('and',) : 0.0769\n", "('rewarding',) : 0.0769\n", "\n", "Bigram Prob: \n", "\n", "('i', 'love') : 0.2000\n", "('love', 'natural') : 0.1000\n", "('natural', 'language') : 0.1000\n", "('language', 'processing') : 0.1000\n", "('programming', 'is') : 0.1000\n", "('is', 'fun') : 0.1000\n", "('fun', 'and') : 0.1000\n", "('and', 'rewarding') : 0.1000\n", "('love', 'programming') : 0.1000\n", "\n", "Trigram Prob: \n", "\n", "('i', 'love', 'natural') : 0.1429\n", "('love', 'natural', 'language') : 0.1429\n", "('natural', 'language', 'processing') : 0.1429\n", "('programming', 'is', 'fun') : 0.1429\n", "('is', 'fun', 'and') : 0.1429\n", "('fun', 'and', 'rewarding') : 0.1429\n", "('i', 'love', 'programming') : 0.1429\n", "Sentence prib (Unigram) : 0.003641329085116068\n", "Sentence prib (Bigram) : 2.0000000000000003e-10\n", "Sentence prib (Trigram) : 1.4285714285714287e-17\n"]}], "source": ["import nltk\n", "from nltk.util import ngrams\n", "from collections import Counter\n", "import math\n", "\n", "sentences = [\"I love natural language processing\",\n", "             \"Programming is fun and rewarding\",\n", "             \"I Love programming\"]\n", "\n", "tokenized_sentences = [nltk.word_tokenize(sentence.lower()) for sentence in sentences]\n", "print(\"Tokenized sentences: \\n\",tokenized_sentences)\n", "\n", "def gen_ngrams(tokenized_sentences, n):\n", "    ngram_list = []\n", "    for sentence in tokenized_sentences:\n", "        ngram_list.extend(ngrams(sentence,n))\n", "    return ngram_list\n", "\n", "uni = gen_ngrams(tokenized_sentences, 1)\n", "bi = gen_ngrams(tokenized_sentences, 2)\n", "tri = gen_ngrams(tokenized_sentences, 3)\n", "\n", "print(\"Unigrams: \\n\",uni,\"\\nBigrams: \\n\",bi,\"\\nTrigrams: \\n\",tri)\n", "\n", "\n", "def calc_ngram_prob(ngram_list, n_minus_1=None):\n", "    ngram_counts = Counter(ngram_list)\n", "    ngram_prob={}\n", "    if n_minus_1:\n", "        n_minus_1_counts = Counter(n_minus_1)\n", "        for ngram in ngram_counts:\n", "            prefix=ngram[:-1]\n", "            ngram_prob[ngram] = ngram_counts[ngram]/n_minus_1[prefix]\n", "    else:\n", "        total = sum(ngram_counts.values())\n", "        for ngram in ngram_counts:\n", "            ngram_prob[ngram] = ngram_counts[ngram]/total\n", "\n", "    return ngram_prob\n", "\n", "uni_prob = calc_ngram_prob(uni)\n", "bi_prob = calc_ngram_prob(bi)\n", "tri_prob = calc_ngram_prob(tri)\n", "\n", "print(\"Unigram Prob: \\n\", )\n", "for uni, prob in uni_prob.items():\n", "    print(f\"{uni} : {prob:.4f}\")\n", "\n", "print(\"\\nBigram Prob: \\n\", )\n", "for bi, prob in bi_prob.items():\n", "    print(f\"{bi} : {prob:.4f}\")\n", "    \n", "print(\"\\nTrigram Prob: \\n\", )\n", "for tri, prob in tri_prob.items():\n", "    print(f\"{tri} : {prob:.4f}\")\n", "\n", "\n", "def calc_sentence_prob(s, n, p):\n", "    tokens = nltk.word_tokenize(s.lower())\n", "    s_ngrams = list(ngrams(tokens, n, pad_left=True, pad_right=True, left_pad_symbol='s', right_pad_symbol='/s'))\n", "    s_prob = 1.0\n", "\n", "    for ngram in s_ngrams:\n", "        pb = p.get(ngram,0)\n", "        if pb==0:\n", "            s_prob *= 0.0001\n", "        else:\n", "            s_prob *= pb\n", "    \n", "    return s_prob\n", "\n", "test = \"I love programming\"\n", "\n", "print(\"Sentence prib (Unigram) :\", calc_sentence_prob(test, 1, uni_prob))\n", "print(\"Sentence prib (Bigram) :\", calc_sentence_prob(test, 2, bi_prob))\n", "print(\"Sentence prib (Trigram) :\", calc_sentence_prob(test, 3, tri_prob))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Unigram Probabilities:\n", "('i',) : 0.1538\n", "('love',) : 0.1538\n", "('natural',) : 0.0769\n", "('language',) : 0.0769\n", "('processing',) : 0.0769\n", "('programming',) : 0.1538\n", "('is',) : 0.0769\n", "('fun',) : 0.0769\n", "('and',) : 0.0769\n", "('rewarding',) : 0.0769\n", "\n", "Bigram Probabilities:\n", "('i', 'love') : 1.0000\n", "('love', 'natural') : 0.5000\n", "('natural', 'language') : 1.0000\n", "('language', 'processing') : 1.0000\n", "('programming', 'is') : 0.5000\n", "('is', 'fun') : 1.0000\n", "('fun', 'and') : 1.0000\n", "('and', 'rewarding') : 1.0000\n", "('love', 'programming') : 0.5000\n", "\n", "Trigram Probabilities:\n", "('i', 'love', 'natural') : 0.5000\n", "('love', 'natural', 'language') : 1.0000\n", "('natural', 'language', 'processing') : 1.0000\n", "('programming', 'is', 'fun') : 1.0000\n", "('is', 'fun', 'and') : 1.0000\n", "('fun', 'and', 'rewarding') : 1.0000\n", "('i', 'love', 'programming') : 0.5000\n", "\n", "Sentence Probabilities:\n", "Unigram: 0.003641329085116068\n", "Bigram: 5e-09\n", "Trigram: 5e-17\n"]}], "source": ["import nltk\n", "from nltk.util import ngrams\n", "from collections import Counter\n", "\n", "sentences = [\"I love natural language processing\",\n", "             \"Programming is fun and rewarding\",\n", "             \"I Love programming\"]\n", "\n", "# Tokenize and lowercase\n", "tokenized = [nltk.word_tokenize(s.lower()) for s in sentences]\n", "\n", "# Generate n-grams\n", "def gen_ngrams(data, n):\n", "    return [ng for sent in data for ng in ngrams(sent, n)]\n", "\n", "uni = gen_ngrams(tokenized, 1)\n", "bi = gen_ngrams(tokenized, 2)\n", "tri = gen_ngrams(tokenized, 3)\n", "\n", "# Calculate n-gram probabilities\n", "def calc_ngram_prob(ngs, base=None):\n", "    ng_counts = Counter(ngs)\n", "    if base:\n", "        base_counts = Counter(base)\n", "        return {ng: ng_counts[ng] / base_counts[ng[:-1]] for ng in ng_counts}\n", "    else:\n", "        total = sum(ng_counts.values())\n", "        return {ng: ng_counts[ng] / total for ng in ng_counts}\n", "\n", "uni_prob = calc_ngram_prob(uni)\n", "bi_prob = calc_ngram_prob(bi, uni)\n", "tri_prob = calc_ngram_prob(tri, bi)\n", "\n", "# Print n-gram probabilities\n", "def print_probs(label, probs):\n", "    print(f\"\\n{label} Probabilities:\")\n", "    for ng, p in probs.items():\n", "        print(f\"{ng} : {p:.4f}\")\n", "\n", "print_probs(\"Unigram\", uni_prob)\n", "print_probs(\"Bigram\", bi_prob)\n", "print_probs(\"Trigram\", tri_prob)\n", "\n", "# Sentence probability calculator\n", "def calc_sentence_prob(sentence, n, probs):\n", "    tokens = nltk.word_tokenize(sentence.lower())\n", "    padded = list(ngrams(tokens, n, pad_left=True, pad_right=True,\n", "                         left_pad_symbol='s', right_pad_symbol='/s'))\n", "    prob = 1.0\n", "    for ng in padded:\n", "        prob *= probs.get(ng, 0.0001)\n", "    return prob\n", "\n", "# Test sentence\n", "test = \"I love programming\"\n", "print(\"\\nSentence Probabilities:\")\n", "print(\"Unigram:\", calc_sentence_prob(test, 1, uni_prob))\n", "print(\"Bigram:\", calc_sentence_prob(test, 2, bi_prob))\n", "print(\"Trigram:\", calc_sentence_prob(test, 3, tri_prob))\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Program 3"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Minimum Edit Distance between 'kitten' and 'sitting' : 3\n", "Minimum Edit Distance between 'flaw' and 'lawn' : 2\n", "Minimum Edit Distance between 'intention' and 'execution' : 5\n", "Minimum Edit Distance between 'sunday' and 'saturday' : 3\n", "Minimum Edit Distance between 'hello' and 'helo' : 1\n", "Minimum Edit Distance between 'abc' and 'def' : 3\n"]}], "source": ["import numpy as np\n", "def min_edit_distance(s1,s2):\n", "    m=len(s1)\n", "    n=len(s2)\n", "\n", "    dp = [[0 for _ in range (n+1)]for _ in range (m+1)]\n", "\n", "    for i in range(m+1):\n", "        dp[i][0]=i\n", "    for j in range (n+1):\n", "        dp[0][j]=j\n", "    for i in range (1,m+1):\n", "        for j in range (1,n+1):\n", "            if s1[i-1] == s2[j-1]:\n", "                dp[i][j] = dp[i-1][j-1]\n", "            else:\n", "                dp[i][j]= min (dp[i-1][j-1],dp[i-1][j],dp[i][j-1])+1\n", "    \n", "    return dp[m][n]\n", "test_cases=[\n", "    (\"kitten\",\"sitting\"),\n", "    (\"flaw\",\"lawn\"),\n", "    (\"intention\",\"execution\"),\n", "    (\"sunday\",\"saturday\"),\n", "    (\"hello\",\"helo\"),\n", "    (\"abc\",\"def\")\n", "]\n", "for s1, s2 in test_cases:\n", "    distance = min_edit_distance(s1,s2)\n", "    print(f\"Minimum Edit Distance between '{s1}' and '{s2}' : {distance}\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[0, 1, 2, 3], [1, 1, 2, 3], [2, 2, 2, 3], [3, 3, 3, 3]]\n", "Minimum Edit Distance between 'abc' and 'def' : 3\n"]}], "source": ["def min_edit_distance(s1,s2):\n", "    m=len(s1)\n", "    n=len(s2)\n", "\n", "    dp = [[0 for _ in range (n+1)]for _ in range (m+1)]\n", "\n", "    for i in range(m+1):\n", "        dp[i][0]=i\n", "    for j in range (n+1):\n", "        dp[0][j]=j\n", "    for i in range (1,m+1):\n", "        for j in range (1,n+1):\n", "            if s1[i-1] == s2[j-1]:\n", "                dp[i][j] = dp[i-1][j-1]\n", "            else:\n", "                dp[i][j]= min (dp[i-1][j-1],dp[i-1][j],dp[i][j-1])+1\n", "    print(dp)\n", "    return dp[m][n]\n", "test_cases=[\n", "    (\"abc\",\"def\"),\n", "\n", "]\n", "for s1, s2 in test_cases:\n", "    distance = min_edit_distance(s1,s2)\n", "    print(f\"Minimum Edit Distance between '{s1}' and '{s2}' : {distance}\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Program 4"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Top-Down Parse: \n", "              S               \n", "      ________|_____           \n", "     |              VP        \n", "     |         _____|___       \n", "     NP       |         NP    \n", "  ___|___     |      ___|___   \n", "Det      N    V    Det      N \n", " |       |    |     |       |  \n", "the     dog chased the     cat\n", "\n", "None\n", "\n", "Bottom-Up Parse: \n", "              S               \n", "      ________|_____           \n", "     |              VP        \n", "     |         _____|___       \n", "     NP       |         NP    \n", "  ___|___     |      ___|___   \n", "Det      N    V    Det      N \n", " |       |    |     |       |  \n", "the     dog chased the     cat\n", "\n", "None\n"]}], "source": ["import nltk\n", "from nltk import CFG, ChartParser\n", "g=CFG.fromstring(\"\"\"\n", "    S -> NP VP\n", "    NP ->Det N\n", "    VP -> V NP\n", "    Det ->'the'\n", "    N ->'dog'|'cat'\n", "    V -> 'chased'|'saw'\n", "    \"\"\")\n", "\n", "s=\"the dog chased the cat\".split()\n", "\n", "def t_d_p(g,s):\n", "    p=nltk.RecursiveDescentParser(g)\n", "    for tree in p.parse(s):\n", "        print(tree.pretty_print())\n", "\n", "def b_u_p(g,s):\n", "    p=nltk.ShiftReduceParser(g)\n", "    for tree in p.parse(s):\n", "        print(tree.pretty_print())\n", "print(\"Top-Down Parse: \")\n", "t_d_p(g,s)\n", "print(\"\\nBottom-Up Parse: \")\n", "b_u_p(g,s)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Top-Down Parse: \n", "(S (NP (Det the) (N dog)) (VP (V chased) (NP (Det the) (N cat))))\n", "\n", "Bottom-Up Parse: \n", "(S (NP (Det the) (N dog)) (VP (V chased) (NP (Det the) (N cat))))\n"]}], "source": ["import nltk\n", "from nltk import CFG, ChartParser\n", "g=CFG.fromstring(\"\"\"\n", "    S -> NP VP\n", "    NP ->Det N\n", "    VP -> V NP\n", "    Det ->'the'\n", "    N ->'dog'|'cat'\n", "    V -> 'chased'|'saw'\n", "    \"\"\")\n", "\n", "s=\"the dog chased the cat\".split()\n", "\n", "def t_d_p(g,s):\n", "    p=nltk.RecursiveDescentParser(g)\n", "    for tree in p.parse(s):\n", "        print(tree)\n", "\n", "def b_u_p(g,s):\n", "    p=nltk.ShiftReduceParser(g)\n", "    for tree in p.parse(s):\n", "        print(tree)\n", "print(\"Top-Down Parse: \")\n", "t_d_p(g,s)\n", "print(\"\\nBottom-Up Parse: \")\n", "b_u_p(g,s)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Top-Down Parse: \n", "               S              \n", "      _________|___            \n", "     |             VP         \n", "     |          ___|___        \n", "     NP        |       NP     \n", "  ___|____     |    ___|___    \n", "Det       N    V  Det      N  \n", " |        |    |   |       |   \n", "the     tiger ate the     food\n", "\n", "None\n", "\n", "Bottom-Up Parse: \n", "               S              \n", "      _________|___            \n", "     |             VP         \n", "     |          ___|___        \n", "     NP        |       NP     \n", "  ___|____     |    ___|___    \n", "Det       N    V  Det      N  \n", " |        |    |   |       |   \n", "the     tiger ate the     food\n", "\n", "None\n"]}], "source": ["import nltk\n", "from nltk import CFG, ChartParser\n", "g=CFG.fromstring(\"\"\"\n", "    S -> NP VP\n", "    NP ->Det N\n", "    VP -> V NP\n", "    Det ->'the'\n", "    N ->'tiger'|'food'\n", "    V -> 'ate'\n", "    \"\"\")\n", "\n", "s=\"the tiger ate the food\".split()\n", "\n", "def t_d_p(g,s):\n", "    p=nltk.RecursiveDescentParser(g)\n", "    for tree in p.parse(s):\n", "        print(tree.pretty_print())\n", "\n", "def b_u_p(g,s):\n", "    p=nltk.ShiftReduceParser(g)\n", "    for tree in p.parse(s):\n", "        print(tree.pretty_print())\n", "print(\"Top-Down Parse: \")\n", "t_d_p(g,s)\n", "print(\"\\nBottom-Up Parse: \")\n", "b_u_p(g,s)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Top-Down Parse: \n", "                    S                     \n", "      ______________|___                   \n", "     |                  VP                \n", "     |         _________|___               \n", "     |        |             NP            \n", "     |        |      _______|___           \n", "     |        |     |   |       PP        \n", "     |        |     |   |    ___|___       \n", "     NP       |     |   |   |       NP    \n", "  ___|___     |     |   |   |    ___|___   \n", "Det      N    V    Det  N   P  Det      N \n", " |       |    |     |   |   |   |       |  \n", "the     cat chased the dog  on the     rug\n", "\n", "None\n", "                    S                         \n", "      ______________|_______                   \n", "     |                      VP                \n", "     |         _____________|_______           \n", "     |        |         |           PP        \n", "     |        |         |        ___|___       \n", "     NP       |         NP      |       NP    \n", "  ___|___     |      ___|___    |    ___|___   \n", "Det      N    V    Det      N   P  Det      N \n", " |       |    |     |       |   |   |       |  \n", "the     cat chased the     dog  on the     rug\n", "\n", "None\n", "\n", "Bottom-Up Parse: \n"]}], "source": ["import nltk\n", "from nltk import CFG, ChartParser\n", "g=CFG.fromstring(\"\"\"\n", "    S -> NP VP\n", "    NP -> Det N | Det N PP\n", "    VP -> V NP | V NP PP \n", "    PP -> P NP\n", "    VP -> V P NP\n", "    Det -> 'the'\n", "    N -> 'dog'|'cat'|'rug'\n", "    V -> 'chased'\n", "    P -> 'on'\n", "    \"\"\")\n", "\n", "s=\"the cat chased the dog on the rug\".split()\n", "\n", "def t_d_p(g,s):\n", "    p=nltk.RecursiveDescentParser(g)\n", "    for tree in p.parse(s):\n", "        print(tree.pretty_print())\n", "\n", "def b_u_p(g,s):\n", "    p=nltk.ShiftReduceParser(g)\n", "    for tree in p.parse(s):\n", "        print(tree.pretty_print())\n", "print(\"Top-Down Parse: \")\n", "t_d_p(g,s)\n", "print(\"\\nBottom-Up Parse: \")\n", "b_u_p(g,s)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Top-Down Parse: \n", "                    S                         \n", "      ______________|_______________           \n", "     |              VP              PP        \n", "     |         _____|___         ___|___       \n", "     NP       |         NP      |       NP    \n", "  ___|___     |      ___|___    |    ___|___   \n", "Det      N    V    Det      N   P  Det      N \n", " |       |    |     |       |   |   |       |  \n", "the     cat chased the     dog  on the     rug\n", "\n", "None\n", "\n", "Bottom-Up Parse: \n", "                    S                         \n", "      ______________|_______________           \n", "     |              VP              PP        \n", "     |         _____|___         ___|___       \n", "     NP       |         NP      |       NP    \n", "  ___|___     |      ___|___    |    ___|___   \n", "Det      N    V    Det      N   P  Det      N \n", " |       |    |     |       |   |   |       |  \n", "the     cat chased the     dog  on the     rug\n", "\n", "None\n"]}], "source": ["import nltk\n", "from nltk import CFG, ChartParser\n", "g = CFG.fromstring(\"\"\"\n", "                   S -> NP VP PP\n", "                   NP -> Det N\n", "                   VP -> V NP\n", "                   PP -> P NP\n", "                   Det -> 'the'\n", "                   N -> 'dog'|'cat'|'rug'\n", "                   V -> 'chased'|'saw'\n", "                   P -> 'on'\n", "                   \"\"\")\n", "\n", "\n", "s=\"the cat chased the dog on the rug\".split()\n", "\n", "def t_d_p(g,s):\n", "    p=nltk.RecursiveDescentParser(g)\n", "    for tree in p.parse(s):\n", "        print(tree.pretty_print())\n", "\n", "def b_u_p(g,s):\n", "    p=nltk.ShiftReduceParser(g)\n", "    for tree in p.parse(s):\n", "        print(tree.pretty_print())\n", "print(\"Top-Down Parse: \")\n", "t_d_p(g,s)\n", "print(\"\\nBottom-Up Parse: \")\n", "b_u_p(g,s)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Program 6\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package brown to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Package brown is already up-to-date!\n", "[nltk_data] Downloading package inaugural to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Package inaugural is already up-to-date!\n", "[nltk_data] Downloading package reuters to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Package reuters is already up-to-date!\n", "[nltk_data] Downloading package udhr to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Package udhr is already up-to-date!\n", "[nltk_data] Downloading package punkt to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import nltk\n", "from nltk.corpus import brown, inaugural, reuters, udhr\n", "from nltk.probability import ConditionalFreqDist\n", "from nltk import pos_tag, word_tokenize\n", "from nltk.tag import UnigramTagger\n", "from nltk.corpus import PlaintextCorpusReader\n", "nltk.download('brown')\n", "nltk.download('inaugural')\n", "nltk.download('reuters')\n", "nltk.download('udhr')\n", "nltk.download('punkt')\n", "nltk.download('averaged_perceptron_tagger')\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- <PERSON> Corpus---\n", "\n", " Categories: ['adventure', 'belles_lettres', 'editorial', 'fiction', 'government', 'hobbies', 'humor', 'learned', 'lore', 'mystery', 'news', 'religion', 'reviews', 'romance', 'science_fiction']\n", "\n", " Words: ['The', '<PERSON>', '<PERSON>', '<PERSON>', 'Jury', 'said', 'Friday', 'an', 'investigation', 'of']\n", "\n", " Sentences: [['The', '<PERSON>', '<PERSON>', 'Grand', 'Jury', 'said', 'Friday', 'an', 'investigation', 'of', \"Atlanta's\", 'recent', 'primary', 'election', 'produced', '``', 'no', 'evidence', \"''\", 'that', 'any', 'irregularities', 'took', 'place', '.'], ['The', 'jury', 'further', 'said', 'in', 'term-end', 'presentments', 'that', 'the', 'City', 'Executive', 'Committee', ',', 'which', 'had', 'over-all', 'charge', 'of', 'the', 'election', ',', '``', 'deserves', 'the', 'praise', 'and', 'thanks', 'of', 'the', 'City', 'of', 'Atlanta', \"''\", 'for', 'the', 'manner', 'in', 'which', 'the', 'election', 'was', 'conducted', '.']]\n", "\n", "--- Inaugural Corpus---\n", "\n", " Files:  ['1789-Washington.txt', '1793-Washington.txt', '1797-Adams.txt', '1801-Jefferson.txt', '1805-Jefferson.txt']\n", "\n", " Words:  ['My', 'fellow', 'citizens', ':', 'I', 'stand', 'here', 'today', 'humbled', 'by']\n", "\n", "--- <PERSON><PERSON> Corpus---\n", "\n", " Categories:  ['acq', 'alum', 'barley', 'bop', 'carcass']\n", "\n", " Words:  ['ASIAN', 'EXPORTERS', 'FEAR', 'DAMAGE', 'FROM', 'U', '.', 'S', '.-', 'JAP<PERSON>']\n", "\n", "--- UDHR ---\n", "\n", " Languages:  ['Abkhaz-Cyrillic+Abkh', 'Abkhaz-UTF8', 'Achehnese-Latin1', 'Achuar-Shiwiar-Latin1', 'Adja-UTF8']\n", "\n", " Words in English: ['Universal', 'Declaration', 'of', 'Human', 'Rights', 'Preamble', 'Whereas', 'recognition', 'of', 'the']\n"]}], "source": ["def study_corpora():\n", "    print(\"\\n--- <PERSON>---\")\n", "    print(\"\\n Categories:\",brown.categories())\n", "    print(\"\\n Words:\",brown.words()[:10])\n", "    print(\"\\n Sentences:\",brown.sents()[:2])\n", "    print(\"\\n--- Inaugural Corpus---\")\n", "    print(\"\\n Files: \",inaugural.fileids()[:5])\n", "    print(\"\\n Words: \",inaugural.words(\"2009-Obama.txt\")[:10])\n", "    print(\"\\n--- <PERSON><PERSON> Corpus---\")\n", "    print(\"\\n Categories: \",reuters.categories()[:5])\n", "    print(\"\\n Words: \",reuters.words(categories='trade')[:10])\n", "    print(\"\\n--- UDHR ---\")\n", "    print(\"\\n Languages: \",udhr.fileids()[:5])\n", "    print(\"\\n Words in English:\",udhr.words('English-Latin1')[:10])\n", "study_corpora()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Avengers.txt', 'Solo Leveling.txt']\n", "['<PERSON>', '<PERSON><PERSON><PERSON>', 'is', 'the', 'protagonist', 'of', '<PERSON>', 'Leveling', ',', 'a']\n", "['Avengers', ':', 'Endgame', 'is', 'a', '2019', 'American', 'superhero', 'film', 'based', 'on', 'the', 'Marvel', 'Comics', 'superhero', 'team', 'the', 'Avengers', '.', 'Produced']\n"]}], "source": ["from nltk.corpus.reader.plaintext import PlaintextCorpusReader\n", "corpus_root=\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\6th Sem\\\\NLP Lab\"\n", "my_corpus=PlaintextCorpusReader(corpus_root,'.*\\.txt')\n", "print(my_corpus.fileids())\n", "print(my_corpus.words('Solo leveling.txt')[:10])\n", "print(my_corpus.words('Avengers.txt')[:20])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Conditional Frequency of 'money':\n", "30\n"]}], "source": ["def study_cfd():\n", "    cfd=ConditionalFreqDist((genre,word.lower()) for genre in brown.categories() for word in brown.words(categories=genre))\n", "    print(\"\\nConditional Frequency of 'money':\")\n", "    print(cfd['news']['money'])\n", "study_cfd()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- <PERSON><PERSON> from Brown Corpus---\n", "[[('The', 'AT'), ('<PERSON>', 'NP-TL'), ('County', 'NN-TL'), ('<PERSON>', 'JJ-TL'), ('Jury', 'NN-TL'), ('said', 'VBD'), ('Friday', 'NR'), ('an', 'AT'), ('investigation', 'NN'), ('of', 'IN'), (\"Atlanta's\", 'NP$'), ('recent', 'JJ'), ('primary', 'NN'), ('election', 'NN'), ('produced', 'VBD'), ('``', '``'), ('no', 'AT'), ('evidence', 'NN'), (\"''\", \"''\"), ('that', 'CS'), ('any', 'DTI'), ('irregularities', 'NNS'), ('took', 'VBD'), ('place', 'NN'), ('.', '.')], [('The', 'AT'), ('jury', 'NN'), ('further', 'RBR'), ('said', 'VBD'), ('in', 'IN'), ('term-end', 'NN'), ('presentments', 'NNS'), ('that', 'CS'), ('the', 'AT'), ('City', 'NN-TL'), ('Executive', 'JJ-TL'), ('Committee', 'NN-TL'), (',', ','), ('which', 'WDT'), ('had', 'HVD'), ('over-all', 'JJ'), ('charge', 'NN'), ('of', 'IN'), ('the', 'AT'), ('election', 'NN'), (',', ','), ('``', '``'), ('deserves', 'VBZ'), ('the', 'AT'), ('praise', 'NN'), ('and', 'CC'), ('thanks', 'NNS'), ('of', 'IN'), ('the', 'AT'), ('City', 'NN-TL'), ('of', 'IN-TL'), ('Atlanta', 'NP-TL'), (\"''\", \"''\"), ('for', 'IN'), ('the', 'AT'), ('manner', 'NN'), ('in', 'IN'), ('which', 'WDT'), ('the', 'AT'), ('election', 'NN'), ('was', 'BEDZ'), ('conducted', 'VBN'), ('.', '.')]]\n", "\n", "--- Tagged Words from Brown Corpus---\n", "[('The', 'AT'), ('<PERSON>', 'NP-TL'), ('County', 'NN-TL'), ('<PERSON>', 'JJ-TL'), ('Jury', 'NN-TL'), ('said', 'VBD'), ('Friday', 'NR'), ('an', 'AT'), ('investigation', 'NN'), ('of', 'IN')]\n"]}], "source": ["def study_tagged_corpus():\n", "    print(\"\\n--- Taggged Sentences from Brown Corpus---\")\n", "    print(brown.tagged_sents(categories='news')[:2])\n", "    print(\"\\n--- Tagged Words from Brown Corpus---\")\n", "    print(brown.tagged_words(categories='news')[:10])\n", "study_tagged_corpus()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Most Frequent Nouns:  [('year', 138), ('years', 102), ('time', 97), ('President', 89), ('state', 88), ('week', 86), ('home', 73), ('man', 72), ('House', 69), ('members', 69)]\n"]}], "source": ["def most_frequent_nouns():\n", "    tagged_words=brown.tagged_words(categories='news')\n", "    nouns=[word for word, tag in tagged_words if tag.startswith('NN')]\n", "    freq_dist=nltk.FreqDist(nouns)\n", "    print(\"\\nMost Frequent Nouns: \",freq_dist.most_common(10))\n", "most_frequent_nouns()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " Word Properties:  {'run': 'verb', 'apple': 'noun', 'quickly': 'adverb'}\n"]}], "source": ["def word_properties():\n", "    word_dict = {'run':'verb','apple':'noun','quickly':'adverb'}\n", "    print(\"\\n Word Properties: \",word_dict)\n", "word_properties()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from nltk.tag import UnigramTagger, RegexpTagger\n", "from nltk.corpus import brown\n", "\n", "# Corrected patterns with commas and valid regex syntax\n", "patterns = [\n", "    (r'.*ing$', 'VBG'),  # Verb (gerund)\n", "    (r'.*ed$', 'VBD'),   # Verb (past tense)\n", "    (r'.*s$', 'NNS'),    # Plural noun\n", "    (r'.*', 'NN')        # Default to noun\n", "]\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[('The', 'NN'), ('cat', 'NN'), ('is', 'NNS'), ('running', 'VBG')]\n", "\n", " Training Sentences:\n", " [[('The', 'AT'), ('<PERSON>', 'NP-TL'), ('County', 'NN-TL'), ('<PERSON>', 'JJ-TL'), ('Jury', 'NN-TL'), ('said', 'VBD'), ('Friday', 'NR'), ('an', 'AT'), ('investigation', 'NN'), ('of', 'IN'), (\"Atlanta's\", 'NP$'), ('recent', 'JJ'), ('primary', 'NN'), ('election', 'NN'), ('produced', 'VBD'), ('``', '``'), ('no', 'AT'), ('evidence', 'NN'), (\"''\", \"''\"), ('that', 'CS'), ('any', 'DTI'), ('irregularities', 'NNS'), ('took', 'VBD'), ('place', 'NN'), ('.', '.')], [('The', 'AT'), ('jury', 'NN'), ('further', 'RBR'), ('said', 'VBD'), ('in', 'IN'), ('term-end', 'NN'), ('presentments', 'NNS'), ('that', 'CS'), ('the', 'AT'), ('City', 'NN-TL'), ('Executive', 'JJ-TL'), ('Committee', 'NN-TL'), (',', ','), ('which', 'WDT'), ('had', 'HVD'), ('over-all', 'JJ'), ('charge', 'NN'), ('of', 'IN'), ('the', 'AT'), ('election', 'NN'), (',', ','), ('``', '``'), ('deserves', 'VBZ'), ('the', 'AT'), ('praise', 'NN'), ('and', 'CC'), ('thanks', 'NNS'), ('of', 'IN'), ('the', 'AT'), ('City', 'NN-TL'), ('of', 'IN-TL'), ('Atlanta', 'NP-TL'), (\"''\", \"''\"), ('for', 'IN'), ('the', 'AT'), ('manner', 'NN'), ('in', 'IN'), ('which', 'WDT'), ('the', 'AT'), ('election', 'NN'), ('was', 'BEDZ'), ('conducted', 'VBN'), ('.', '.')], ...]\n", "\n", " Test Sentences :\n", " [['In', \"<PERSON>'s\", 'day', '--', 'and', 'until', 'this', 'year', '--', 'the', 'schedule', 'was', '154', 'games', '.'], ['Baseball', 'commissioner', '<PERSON>', 'Frick', 'has', 'ruled', 'that', \"<PERSON>'s\", 'record', 'will', 'remain', 'official', 'unless', 'it', 'is', 'broken', 'in', '154', 'games', '.'], [')'], ['``', 'Even', 'on', 'the', 'basis', 'of', '154', 'games', ',', 'this', 'is', 'the', 'ideal', 'situation', \"''\", ',', 'insists', '<PERSON>', '<PERSON>', ',', 'now', 'vice-president', 'of', 'the', 'Chicago', 'White', 'Sox', '.'], ['``', 'It', 'has', 'to', 'be', 'easier', 'with', 'two', 'of', 'them', '.'], ['How', 'can', 'you', 'walk', '<PERSON>', 'to', 'get', 'to', 'Mantle', \"''\", '?', '?'], ['Roommates', ':'], ['Neither', 'Mantle', 'nor', 'Maris', ',', 'understandably', ',', 'will', 'predict', '60', 'home', 'runs', 'for', 'himself', '.'], ['Although', 'both', 'concede', 'they', 'would', 'like', 'to', 'hit', '60', ',', 'they', 'stick', 'primarily', 'to', 'the', 'baseball', \"player's\", 'standard', 'quote', ':', '``', 'The', 'important', 'thing', 'is', 'to', 'win', 'the', 'pennant', \"''\", '.'], ['But', 'one', 'thing', 'is', 'for', 'certain', ':', 'There', 'is', 'no', 'dissension', 'between', 'Mantle', ',', 'the', 'American', \"League's\", 'Most', 'Valuable', 'Player', 'in', '1956', 'and', '1957', ',', 'and', 'Maris', ',', 'the', 'MVP', 'in', '1960', '.'], ['Each', 'enjoys', 'seeing', 'the', 'other', 'hit', 'home', 'runs', '(', '``', 'I', 'hope', 'Roger', 'hits', '80', \"''\", ',', 'Mantle', 'says', ')', ',', 'and', 'each', 'enjoys', 'even', 'more', 'seeing', 'himself', 'hit', 'home', 'runs', '(', '``', 'and', 'I', 'hope', 'I', 'hit', '81', \"''\", ')', '.'], ['The', 'sluggers', 'get', 'along', 'so', 'well', 'in', 'fact', ',', 'that', 'with', 'their', 'families', 'at', 'home', 'for', 'the', 'summer', '(', \"Mantle's\", 'in', 'Dallas', ',', \"Maris's\", 'in', 'Kansas', 'City', ')', ',', 'they', 'are', 'rooming', 'together', '.'], ['Mantle', ',', 'Maris', ',', 'and', 'Bob', 'Cerv', ',', 'a', 'utility', 'outfielder', ',', 'share', 'an', 'apartment', 'in', 'Jamaica', ',', 'Long', 'Island', ',', 'not', 'far', 'from', 'New', 'York', 'International', 'Airport', '.'], ['The', 'three', 'pay', '$251', 'a', 'month', 'for', 'four', 'rooms', '(', 'kitchen', ',', 'dining', 'room', ',', 'living', 'room', ',', 'and', 'bedroom', ')', ',', 'with', 'air-conditioning', 'and', 'new', 'modern', 'furniture', '.'], ['Mantle', 'and', 'Cerv', 'use', 'the', 'twin', 'beds', 'in', 'the', 'bedroom', ';', ';'], ['Maris', 'sleeps', 'on', 'a', 'green', 'studio', 'couch', 'in', 'the', 'living', 'room', '.'], ['They', 'divide', 'up', 'the', 'household', 'chores', ':', 'Cerv', 'does', 'most', 'of', 'the', 'cooking', '(', 'breakfast', 'and', 'sandwich', 'snacks', ',', 'with', 'dinner', 'out', ')', ',', 'Mantle', 'supplies', 'the', 'transportation', '(', 'a', 'white', '1961', 'Oldsmobile', 'convertible', ')', ',', 'and', 'Maris', 'drives', 'the', '25-minute', 'course', 'from', 'the', 'apartment', 'house', 'to', 'Yankee', 'Stadium', '.'], ['Mantle', ',', 'Maris', ',', 'and', 'Cerv', 'probably', 'share', 'one', 'major-league', 'record', 'already', ':', 'Among', 'them', ',', 'they', 'have', 'fifteen', 'children', '--', 'eight', 'for', 'Cerv', ',', 'four', 'for', 'Mantle', ',', 'and', 'three', 'for', 'Maris', '.'], ['As', 'roommates', ',', 'teammates', ',', 'and', 'home-run', 'mates', ',', 'Mantle', ',', '29', ',', 'who', 'broke', 'in', 'with', 'the', 'Yankees', 'ten', 'years', 'ago', ',', 'and', 'Maris', ',', '26', ',', 'who', 'came', 'to', 'the', 'Yankees', 'from', 'Kansas', 'City', 'two', 'years', 'ago', ',', 'have', 'strikingly', 'similar', 'backgrounds', '.'], ['Both', 'were', 'scholastic', 'stars', 'in', 'football', ',', 'basketball', ',', 'and', 'baseball', '(', 'Mantle', 'in', 'Commerce', ',', 'Okla.', ',', 'Maris', 'in', 'Fargo', ',', 'N.D.', ')', ';', ';'], ['as', 'halfbacks', ',', 'both', 'came', 'close', 'to', 'playing', 'football', 'at', 'the', 'University', 'of', 'Oklahoma', '(', '``', 'Sometimes', 'in', 'the', 'minors', \"''\", ',', 'Maris', 'recalls', ',', '``', 'I', 'wished', 'I', 'had', 'gone', 'to', 'Oklahoma', \"''\", ')', '.'], ['To', 'an', 'extent', ',', 'the', 'two', 'even', 'look', 'alike', '.'], ['Both', 'have', 'blue', 'eyes', 'and', 'short', 'blond', 'hair', '.'], ['Both', 'are', '6', 'feet', 'tall', 'and', 'weigh', 'between', '195', 'and', '200', 'pounds', ',', 'but', 'Mantle', ',', 'incredibly', 'muscular', '(', 'he', 'has', 'a', '17-1/2-inch', 'neck', ')', ',', 'looks', 'bigger', '.'], ['With', 'their', 'huge', 'backs', 'and', 'overdeveloped', 'shoulders', ',', 'both', 'must', 'have', 'their', 'clothes', 'made', 'to', 'order', '.'], ['Maris', 'purchases', '$100', 'suits', 'from', \"Simpson's\", 'in', 'New', 'York', '.'], ['Mantle', ',', 'more', 'concerned', 'with', 'dress', ',', 'buys', 'his', 'suits', 'four', 'at', 'a', 'time', 'at', 'Neiman-Marcus', 'in', 'Dallas', 'and', 'pays', 'as', 'much', 'as', '$250', 'each', '.'], ['Light', 'reading', ':'], ['Neither', 'Mantle', 'nor', 'Maris', 'need', 'fear', 'being', 'classified', 'an', 'intellectual', ',', 'but', 'lately', 'Mantle', 'has', 'shown', 'unusual', 'devotion', 'to', 'an', 'intellectual', 'opus', ',', 'Henry', \"Miller's\", '``', 'Tropic', 'of', 'Cancer', \"''\", '.'], ['Mantle', 'so', 'appreciated', \"Miller's\", 'delicate', 'literary', 'style', 'that', 'he', 'broadened', \"teammates'\", 'minds', 'by', 'reading', 'sensitive', 'passages', 'aloud', 'during', 'road', 'trips', '.'], ['Mantle', 'is', 'not', 'normally', 'given', 'to', 'public', 'speaking', '--', 'or', ',', 'for', 'that', 'matter', ',', 'to', 'private', 'speaking', '.'], ['``', 'What', 'do', 'you', 'and', 'Mickey', 'talk', 'about', 'at', 'home', \"''\", '?', '?'], ['A', 'reporter', 'asked', 'Maris', 'recently', '.'], ['``', 'To', 'tell', 'you', 'the', 'truth', \"''\", ',', 'Maris', 'said', ',', '``', 'Mickey', \"don't\", 'talk', 'much', \"''\", '.'], ['This', 'is', 'no', 'surprising', 'trait', 'for', 'a', 'ballplayer', '.'], ['What', 'is', 'surprising', 'and', 'pleasant', 'is', 'that', 'Mantle', 'and', 'Maris', ',', 'under', 'constant', 'pressure', 'from', 'writers', 'and', 'photographers', ',', 'are', 'trying', 'to', 'be', 'cooperative', '.'], ['Of', 'the', 'two', ',', 'Mantle', 'is', 'by', 'nature', 'the', 'less', 'outgoing', ',', 'Maris', 'the', 'more', 'outspoken', '.'], ['But', 'last', 'week', ',', 'when', 'a', 'reporter', 'was', 'standing', 'near', \"Mantle's\", 'locker', ',', 'Mickey', 'walked', 'up', 'and', 'volunteered', 'an', 'anecdote', '.'], ['``', 'See', 'that', 'kid', \"''\", '?', '?'], ['He', 'said', ',', 'pointing', 'to', 'a', 'dark-haired', '11-year-old', 'boy', '.'], ['``', \"That's\", '(', 'Yogi', ')', \"Berra's\", '.'], [\"I'll\", 'never', 'forget', 'one', 'time', 'I', 'struck', 'out', 'three', 'times', ',', 'dropped', 'a', 'fly', 'ball', ',', 'and', 'we', 'lost', 'the', 'game', '.'], ['I', 'came', 'back', ',', 'sitting', 'by', 'my', 'locker', ',', 'feeling', 'real', 'low', ',', 'and', 'the', 'kid', 'walks', 'over', 'to', 'me', ',', 'looks', 'up', ',', 'and', 'says', ':', \"'\", 'You', 'stunk', \"'\", \"''\", '.'], ['Maris', ',', 'in', 'talking', 'to', 'reporters', ',', 'tries', 'to', 'answer', 'all', 'questions', 'candidly', 'and', 'fully', ',', 'but', 'on', 'rare', 'occasions', ',', 'he', 'shuns', 'newsmen', '.'], ['``', 'When', \"I've\", 'made', 'a', 'dumb', 'play', \"''\", ',', 'he', 'says', ',', '``', 'I', \"don't\", 'want', 'to', 'talk', 'to', 'anyone', '.'], [\"I'm\", 'angry', \"''\", '.'], ['By', 'his', 'own', 'confession', ',', 'Maris', 'is', 'an', 'angry', 'young', 'man', '.'], ['Benched', 'at', 'Tulsa', 'in', '1955', ',', 'he', 'told', 'manager', 'Dutch', 'Meyer', ':', '``', 'I', \"can't\", 'play', 'for', 'you', '.'], ['Send', 'me', 'where', 'I', 'can', 'play', \"''\", '.'], ['(', 'Meyer', 'sent', 'him', 'to', 'Reading', ',', 'Pa.', '.']]\n", "\n", " Unigram Tagging Example: [('In', 'IN'), (\"<PERSON>'s\", 'NP$'), ('day', 'NN'), ('--', '--'), ('and', 'CC'), ('until', 'IN'), ('this', 'DT'), ('year', 'NN'), ('--', '--'), ('the', 'AT'), ('schedule', 'NN'), ('was', 'BEDZ'), ('154', None), ('games', 'NNS'), ('.', '.')]\n"]}], "source": ["def unigram_tagger():\n", "    training_sentences=brown.tagged_sents(categories='news')[:4000]\n", "    print(\"\\n Training Sentences:\\n\",training_sentences)\n", "    test_sentences=brown.sents(categories='news')[4000:4050]\n", "    print(\"\\n Test Sentences :\\n\",test_sentences)\n", "    tagger=UnigramTagger(training_sentences)\n", "    print(\"\\n Unigram Tagging Example:\",tagger.tag(test_sentences[0]))\n", "rule_based_tagger=RegexpTagger(patterns)\n", "sentences=['The','cat','is','running']\n", "print(rule_based_tagger.tag(sentences))\n", "unigram_tagger()\n", "def word_segementation(text,word_corpus):\n", "    def segment(text,word_corpus):\n", "        segmented=[]\n", "        while text:\n", "            for i in range (len (text),0,-1):\n", "                if text[:i] in word_corpus:\n", "                    segmented.append(text[i])\n", "                    text=text[i:]\n", "                    break\n", "            else:\n", "                    segment.append(text[0])\n", "                    test=text[1:]\n", "        return segmented\n", "    words=set(brown.words())\n", "    segmented_text=segment(text,words)\n", "    print(\"\\n Segmented Words:\",segmented_text)\n", "    print(\"Word Score:\",{word : len(word) for word in segmented_text})\n", "    word_segementation('thequickbrownfox', set(brown.words()))\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " Segmented Words: ['the', 'quick', 'brown', 'fox']\n", "Word Score: {'the': 3, 'quick': 5, 'brown': 5, 'fox': 3}\n"]}], "source": ["def word_segementation(text,word_corpus):\n", "    def segment(text,word_corpus):\n", "        segmented=[]\n", "        while text:\n", "            for i in range (len (text),0,-1):\n", "                if text[:i] in word_corpus:\n", "                    segmented.append(text[:i])\n", "                    text=text[i:]\n", "                    break\n", "            else:\n", "                segment.append(text[0])\n", "                test=text[1:]\n", "        return segmented\n", "    words=set(brown.words())\n", "    segmented_text=segment(text,words)\n", "    print(\"\\n Segmented Words:\",segmented_text)\n", "    print(\"Word Score:\",{word : len(word) for word in segmented_text})\n", "word_segementation('thequickbrownfox', set(brown.words()))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Program 5"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data: [(['fun', 'couple', 'love', 'love'], 'comedy'), (['fast', 'furious', 'shoot'], 'action'), (['couple', 'fly', 'fast', 'fun', 'fun'], 'comedy'), (['furious', 'shoot', 'shoot', 'fun'], 'action'), (['fly', 'fast', 'shoot', 'love'], 'action')]\n", "\n", "Vocabulary: {'fly', 'fast', 'couple', 'fun', 'furious', 'shoot', 'love'}\n", "Class word counts: {'comedy': defaultdict(<class 'int'>, {'fun': 3, 'couple': 2, 'love': 2, 'fly': 1, 'fast': 1}), 'action': defaultdict(<class 'int'>, {'fast': 2, 'furious': 2, 'shoot': 4, 'fun': 1, 'fly': 1, 'love': 1})}\n", "Class counts: {'comedy': 2, 'action': 3}\n", "\n", "Total words per class: {'comedy': 9, 'action': 11}\n", "Priors: {'comedy': 0.4, 'action': 0.6}\n", "\n", "Posteriors (Normal probabilities):\n", "comedy: 7.324218750000001e-05\n", "action: 0.00017146776406035664\n", "\n", "Predicted label: action\n"]}], "source": ["from collections import defaultdict\n", "import math\n", "\n", "# Dataset\n", "reviews = [\n", "    (\"fun, couple, love, love\", \"comedy\"),\n", "    (\"fast, furious, shoot\", \"action\"),\n", "    (\"couple, fly, fast, fun, fun\", \"comedy\"),\n", "    (\"furious, shoot, shoot, fun\", \"action\"),\n", "    (\"fly, fast, shoot, love\", \"action\")\n", "]\n", "\n", "# Input document to classify\n", "d = \"fast, couple, shoot, fly\".split(\", \")\n", "\n", "# Preprocessing\n", "def preprocess(data):\n", "    tokens = []\n", "    for doc, label in data:\n", "        words = [word.strip() for word in doc.split(\",\")]\n", "        tokens.append((words, label))\n", "    return tokens\n", "\n", "data = preprocess(reviews)\n", "print(\"Data:\", data)\n", "\n", "# Build vocabulary and counts\n", "vocab = set()\n", "class_word_counts = defaultdict(lambda: defaultdict(int))\n", "class_counts = defaultdict(int)\n", "\n", "for words, label in data:\n", "    class_counts[label] += 1\n", "    for word in words:\n", "        class_word_counts[label][word] += 1\n", "        vocab.add(word)\n", "\n", "print(\"\\nVocabulary:\", vocab)\n", "print(\"Class word counts:\", dict(class_word_counts))\n", "print(\"Class counts:\", dict(class_counts))\n", "\n", "vocab_size = len(vocab)\n", "\n", "# Total words per class\n", "class_total_words = {label: sum(class_word_counts[label].values()) for label in class_counts}\n", "print(\"\\nTotal words per class:\", class_total_words)\n", "\n", "# Prior probabilities\n", "total_docs = sum(class_counts.values())\n", "priors = {label: class_counts[label] / total_docs for label in class_counts}\n", "print(\"Priors:\", priors)\n", "\n", "# Likelihood function with La<PERSON> smoothing\n", "def likelihood(word, label):\n", "    return (class_word_counts[label][word] + 1) / (class_total_words[label] + vocab_size)\n", "\n", "# Posterior probabilities (WITHOUT LOGS)\n", "posteriors = {}\n", "\n", "for label in class_counts:\n", "    prob = priors[label]\n", "    for word in d:\n", "        prob *= likelihood(word, label)\n", "    posteriors[label] = prob\n", "\n", "print(\"\\nPosteriors (Normal probabilities):\")\n", "for label, prob in posteriors.items():\n", "    print(f\"{label}: {prob}\")\n", "\n", "# Prediction\n", "predicted_label = max(posteriors, key=posteriors.get)\n", "print(\"\\nPredicted label:\", predicted_label)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Program 7\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " Synonyms: 'active' :\n", "  active,active_agent,active_voice,alive,combat-ready,dynamic,fighting,participating\n", "\n", " Antonyms: 'active' :\n", " dormant,extinct,inactive,passive,passive_voice,quiet,stative\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package wordnet to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Package wordnet is already up-to-date!\n"]}], "source": ["import nltk\n", "from nltk.corpus import wordnet\n", "\n", "nltk.download('wordnet')\n", "\n", "def get_synonyms_antonyms(word):\n", "    synonyms = set()\n", "    antonyms = set()\n", "\n", "    for synset in wordnet.synsets(word):\n", "        for lemma in synset.lemmas():\n", "            synonyms.add(lemma.name())\n", "            if lemma.antonyms():\n", "                antonyms.add(lemma.antonyms()[0].name())\n", "    print(f\"\\n Synonyms: '{word}' :\\n \",','.join(sorted(synonyms)))\n", "    print(f\"\\n Antonyms: '{word}' :\\n\",','.join(sorted(antonyms)))\n", "\n", "get_synonyms_antonyms('active')\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Synset('car.n.01')]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["wordnet.synsets('motorcar')"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["['car', 'auto', 'automobile', 'machine', 'motorcar']"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["wordnet.synset('car.n.01').lemma_names()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Program 8"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "Failed to import transformers.models.marian.modeling_marian because of the following error (look up to see its traceback):\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\tensorflow\\python\\pywrap_tensorflow.py\", line 73, in <module>\n    from tensorflow.python._pywrap_tensorflow_internal import *\nImportError: DLL load failed while importing _pywrap_tensorflow_internal: A dynamic link library (DLL) initialization routine failed.\n\n\nFailed to load the native TensorFlow runtime.\nSee https://www.tensorflow.org/install/errors for some common causes and solutions.\nIf you need help, create an issue at https://github.com/tensorflow/tensorflow/issues and include the entire stack trace above this error message.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mImportError\u001b[0m                               Traceback (most recent call last)", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\tensorflow\\python\\pywrap_tensorflow.py:73\u001b[0m\n\u001b[0;32m     72\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 73\u001b[0m   \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpython\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_pywrap_tensorflow_internal\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;241m*\u001b[39m\n\u001b[0;32m     74\u001b[0m \u001b[38;5;66;03m# This try catch logic is because there is no bazel equivalent for py_extension.\u001b[39;00m\n\u001b[0;32m     75\u001b[0m \u001b[38;5;66;03m# Externally in opensource we must enable exceptions to load the shared object\u001b[39;00m\n\u001b[0;32m     76\u001b[0m \u001b[38;5;66;03m# by exposing the PyInit symbols with pybind. This error will only be\u001b[39;00m\n\u001b[0;32m     77\u001b[0m \u001b[38;5;66;03m# caught internally or if someone changes the name of the target _pywrap_tensorflow_internal.\u001b[39;00m\n\u001b[0;32m     78\u001b[0m \n\u001b[0;32m     79\u001b[0m \u001b[38;5;66;03m# This logic is used in other internal projects using py_extension.\u001b[39;00m\n", "\u001b[1;31mImportError\u001b[0m: DLL load failed while importing _pywrap_tensorflow_internal: A dynamic link library (DLL) initialization routine failed.", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mImportError\u001b[0m                               Traceback (most recent call last)", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\transformers\\utils\\import_utils.py:1967\u001b[0m, in \u001b[0;36m_LazyModule._get_module\u001b[1;34m(self, module_name)\u001b[0m\n\u001b[0;32m   1966\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 1967\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mimportlib\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mimport_module\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m.\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mmodule_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;18;43m__name__\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1968\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[1;32mC:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\importlib\\__init__.py:126\u001b[0m, in \u001b[0;36mimport_module\u001b[1;34m(name, package)\u001b[0m\n\u001b[0;32m    125\u001b[0m         level \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m--> 126\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_bootstrap\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_gcd_import\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m[\u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m:\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpackage\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1050\u001b[0m, in \u001b[0;36m_gcd_import\u001b[1;34m(name, package, level)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1027\u001b[0m, in \u001b[0;36m_find_and_load\u001b[1;34m(name, import_)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1006\u001b[0m, in \u001b[0;36m_find_and_load_unlocked\u001b[1;34m(name, import_)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:688\u001b[0m, in \u001b[0;36m_load_unlocked\u001b[1;34m(spec)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap_external>:883\u001b[0m, in \u001b[0;36mexec_module\u001b[1;34m(self, module)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:241\u001b[0m, in \u001b[0;36m_call_with_frames_removed\u001b[1;34m(f, *args, **kwds)\u001b[0m\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\transformers\\models\\marian\\modeling_marian.py:37\u001b[0m\n\u001b[0;32m     30\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodeling_outputs\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     31\u001b[0m     BaseModelOutput,\n\u001b[0;32m     32\u001b[0m     BaseModelOutputWithPastAndCrossAttentions,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     35\u001b[0m     Seq2SeqModelOutput,\n\u001b[0;32m     36\u001b[0m )\n\u001b[1;32m---> 37\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodeling_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m PreTrainedModel\n\u001b[0;32m     38\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     39\u001b[0m     add_end_docstrings,\n\u001b[0;32m     40\u001b[0m     add_start_docstrings,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     43\u001b[0m     replace_return_docstrings,\n\u001b[0;32m     44\u001b[0m )\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\transformers\\modeling_utils.py:69\u001b[0m\n\u001b[0;32m     65\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mintegrations\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtensor_parallel\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     66\u001b[0m     SUPPORTED_TP_STYLES,\n\u001b[0;32m     67\u001b[0m     shard_and_distribute_module,\n\u001b[0;32m     68\u001b[0m )\n\u001b[1;32m---> 69\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mloss\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mloss_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m LOSS_MAPPING\n\u001b[0;32m     70\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpytorch_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (  \u001b[38;5;66;03m# noqa: F401\u001b[39;00m\n\u001b[0;32m     71\u001b[0m     Conv1D,\n\u001b[0;32m     72\u001b[0m     apply_chunking_to_forward,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     77\u001b[0m     prune_linear_layer,\n\u001b[0;32m     78\u001b[0m )\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\transformers\\loss\\loss_utils.py:21\u001b[0m\n\u001b[0;32m     19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtorch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mnn\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m BCEWithLogitsLoss, MSELoss\n\u001b[1;32m---> 21\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mloss_deformable_detr\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DeformableDetrForObjectDetectionLoss, DeformableDetrForSegmentationLoss\n\u001b[0;32m     22\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mloss_for_object_detection\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ForObjectDetectionLoss, ForSegmentationLoss\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\transformers\\loss\\loss_deformable_detr.py:4\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mtorch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mnn\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mnn\u001b[39;00m\n\u001b[1;32m----> 4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mimage_transforms\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m center_to_corners_format\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m is_scipy_available\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\transformers\\image_transforms.py:47\u001b[0m\n\u001b[0;32m     46\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_tf_available():\n\u001b[1;32m---> 47\u001b[0m     \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mtf\u001b[39;00m\n\u001b[0;32m     49\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_flax_available():\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\tensorflow\\__init__.py:40\u001b[0m\n\u001b[0;32m     39\u001b[0m \u001b[38;5;66;03m# Do not remove this line; See https://github.com/tensorflow/tensorflow/issues/42596\u001b[39;00m\n\u001b[1;32m---> 40\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpython\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m pywrap_tensorflow \u001b[38;5;28;01mas\u001b[39;00m _pywrap_tensorflow  \u001b[38;5;66;03m# pylint: disable=unused-import\u001b[39;00m\n\u001b[0;32m     41\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpyt<PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtools\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m module_util \u001b[38;5;28;01mas\u001b[39;00m _module_util\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\tensorflow\\python\\pywrap_tensorflow.py:88\u001b[0m\n\u001b[0;32m     87\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m:\n\u001b[1;32m---> 88\u001b[0m   \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m(\n\u001b[0;32m     89\u001b[0m       \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtraceback\u001b[38;5;241m.\u001b[39mformat_exc()\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m     90\u001b[0m       \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mFailed to load the native TensorFlow runtime.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m     91\u001b[0m       \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mSee https://www.tensorflow.org/install/errors \u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m     92\u001b[0m       \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfor some common causes and solutions.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m     93\u001b[0m       \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mIf you need help, create an issue \u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m     94\u001b[0m       \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mat https://github.com/tensorflow/tensorflow/issues \u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m     95\u001b[0m       \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mand include the entire stack trace above this error message.\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     97\u001b[0m \u001b[38;5;66;03m# pylint: enable=wildcard-import,g-import-not-at-top,unused-import,line-too-long\u001b[39;00m\n", "\u001b[1;31mImportError\u001b[0m: Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\tensorflow\\python\\pywrap_tensorflow.py\", line 73, in <module>\n    from tensorflow.python._pywrap_tensorflow_internal import *\nImportError: DLL load failed while importing _pywrap_tensorflow_internal: A dynamic link library (DLL) initialization routine failed.\n\n\nFailed to load the native TensorFlow runtime.\nSee https://www.tensorflow.org/install/errors for some common causes and solutions.\nIf you need help, create an issue at https://github.com/tensorflow/tensorflow/issues and include the entire stack trace above this error message.", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01mtransformers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m <PERSON>, <PERSON><PERSON><PERSON><PERSON>\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mevaluate\u001b[39;00m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;66;03m# src = \"fr\"\u001b[39;00m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1075\u001b[0m, in \u001b[0;36m_handle_fromlist\u001b[1;34m(module, fromlist, import_, recursive)\u001b[0m\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\transformers\\utils\\import_utils.py:1956\u001b[0m, in \u001b[0;36m_LazyModule.__getattr__\u001b[1;34m(self, name)\u001b[0m\n\u001b[0;32m   1954\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_class_to_module\u001b[38;5;241m.\u001b[39mkeys():\n\u001b[0;32m   1955\u001b[0m     module \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_module(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_class_to_module[name])\n\u001b[1;32m-> 1956\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mgetattr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mmodule\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1957\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_modules:\n\u001b[0;32m   1958\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_module(name)\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\transformers\\utils\\import_utils.py:1955\u001b[0m, in \u001b[0;36m_LazyModule.__getattr__\u001b[1;34m(self, name)\u001b[0m\n\u001b[0;32m   1953\u001b[0m     value \u001b[38;5;241m=\u001b[39m Placeholder\n\u001b[0;32m   1954\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_class_to_module\u001b[38;5;241m.\u001b[39mkeys():\n\u001b[1;32m-> 1955\u001b[0m     module \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_module\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_class_to_module\u001b[49m\u001b[43m[\u001b[49m\u001b[43mname\u001b[49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1956\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(module, name)\n\u001b[0;32m   1957\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_modules:\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\transformers\\utils\\import_utils.py:1969\u001b[0m, in \u001b[0;36m_LazyModule._get_module\u001b[1;34m(self, module_name)\u001b[0m\n\u001b[0;32m   1967\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m importlib\u001b[38;5;241m.\u001b[39mimport_module(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m+\u001b[39m module_name, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m)\n\u001b[0;32m   1968\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m-> 1969\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[0;32m   1970\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFailed to import \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmodule_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m because of the following error (look up to see its\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1971\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m traceback):\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1972\u001b[0m     ) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01me\u001b[39;00m\n", "\u001b[1;31mRuntimeError\u001b[0m: Failed to import transformers.models.marian.modeling_marian because of the following error (look up to see its traceback):\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\tensorflow\\python\\pywrap_tensorflow.py\", line 73, in <module>\n    from tensorflow.python._pywrap_tensorflow_internal import *\nImportError: DLL load failed while importing _pywrap_tensorflow_internal: A dynamic link library (DLL) initialization routine failed.\n\n\nFailed to load the native TensorFlow runtime.\nSee https://www.tensorflow.org/install/errors for some common causes and solutions.\nIf you need help, create an issue at https://github.com/tensorflow/tensorflow/issues and include the entire stack trace above this error message."]}], "source": ["from transformers import MarianMTModel, MarianTokenizer\n", "import evaluate\n", "# src = \"fr\"\n", "src = \"hi\"\n", "target = \"en\"\n", "\n", "model_name = f\"Helsinki-NLP/opus-mt-{src}-{target}\"\n", "tokenizer = MarianTokenizer.from_pretrained(model_name)\n", "model = MarianMTModel.from_pretrained(model_name)\n", "def translate(texts, tokenizer, model):\n", "    inputs = tokenizer(texts, return_tensors=\"pt\", padding=True)\n", "    outputs = model.generate(**inputs)\n", "    return [tokenizer.decode(t, skip_special_tokens=True) for t in outputs]\n", "# parallel_corpus = [\n", "#     (\"Bon<PERSON><PERSON>\",\"Hello\"),\n", "#     (\"<PERSON><PERSON><PERSON> beaucoup\",\"Thank you very much\"),\n", "#     (\"Comment ca va?\",\"How are you?\"),\n", "#     (\"Je suis etudiant\",\"I am a student\")\n", "# ]\n", "\n", "parallel_corpus = [\n", "    (\"नमस्ते\",\"Hello\"),\n", "    (\"धन्यवाद\",\"Thank you\"),\n", "    (\"आप कैसे हैं?\",\"How are you?\"),\n", "    (\"मैं छात्र हूं\",\"I am a student\")\n", "]\n", "src_texts = [pair[0] for pair in parallel_corpus]\n", "back_translated_texts = [pair[1] for pair in parallel_corpus]\n", "\n", "augmented_texts = parallel_corpus + [(bt, st) for st, bt in zip(back_translated_texts, translate(src_texts, tokenizer, model))]\n", "train_src = [pair[0] for pair in augmented_texts]\n", "train_target = [pair[1] for pair in augmented_texts]\n", "# tests = [\"Merci beaucoup\",\"Je suis content\",\"Merci beaucoup\",\"Bonjour\"]\n", "tests = [\"धन्यवाद\",\"आप कैसे हैं?\",\"नमस्ते\"]\n", "\n", "translations = translate(tests, tokenizer, model)\n", "\n", "for src, pred in zip(tests, translations):\n", "    print(f\"{src} -> {pred}\")\n", "# test_src = [\"Merci beaucoup\",\"Comment ca va?\",\"Il fait bean aujourd'hui\",]\n", "test_src = [\"धन्यवाद\",\"आप कैसे हैं?\",\"मौसम अच्छा है आज\",]\n", "\n", "true_ref = [\"Thank you very much\",\"How are you?\",\"The weather is nice today\",]\n", "\n", "predictions = translate(test_src, tokenizer, model)\n", "bleu = evaluate.load(\"bleu\")\n", "r = bleu.compute(predictions=predictions,references=true_ref)\n", "for src, pred, ref in zip(test_src, predictions, true_ref):\n", "    print(f\"Source: {src}\")\n", "    print(f\"Predicted: {pred}\")\n", "    print(f\"Reference: {ref}\")\n", "    print()\n", "print(f\"BLEU: {r['bleu']}\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "5a4ee9caf3efff372bffc064d50b3d6f485b7d609790dc31d6c93dfc1f3be82d"}}}, "nbformat": 4, "nbformat_minor": 2}