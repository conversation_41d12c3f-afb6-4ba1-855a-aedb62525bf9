{"cells": [{"cell_type": "code", "execution_count": 29, "id": "add38901", "metadata": {}, "outputs": [], "source": ["from transformers import MarianMTModel, MarianTokenizer\n", "import evaluate"]}, {"cell_type": "code", "execution_count": 30, "id": "8f131353", "metadata": {}, "outputs": [], "source": ["# src = \"fr\"\n", "src = \"hi\"\n", "target = \"en\"\n", "\n", "model_name = f\"Helsinki-NLP/opus-mt-{src}-{target}\""]}, {"cell_type": "code", "execution_count": 31, "id": "73c6e2b5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["e:\\Anaconda\\Lib\\site-packages\\transformers\\models\\marian\\tokenization_marian.py:175: UserWarning: Recommended: pip install sacremoses.\n", "  warnings.warn(\"Recommended: pip install sacremoses.\")\n"]}], "source": ["tokenizer = MarianTokenizer.from_pretrained(model_name)\n", "model = MarianMTModel.from_pretrained(model_name)"]}, {"cell_type": "code", "execution_count": 32, "id": "e62ccf0b", "metadata": {}, "outputs": [], "source": ["def translate(texts, tokenizer, model):\n", "    inputs = tokenizer(texts, return_tensors=\"pt\", padding=True)\n", "    outputs = model.generate(**inputs)\n", "    return [tokenizer.decode(t, skip_special_tokens=True) for t in outputs]"]}, {"cell_type": "code", "execution_count": null, "id": "6a0a8e0a", "metadata": {}, "outputs": [], "source": ["# parallel_corpus = [\n", "#     (\"Bon<PERSON><PERSON>\",\"Hello\"),\n", "#     (\"<PERSON><PERSON><PERSON> beaucoup\",\"Thank you very much\"),\n", "#     (\"Comment ca va?\",\"How are you?\"),\n", "#     (\"Je suis etudiant\",\"I am a student\")\n", "# ]\n", "\n", "parallel_corpus = [\n", "    (\"नमस्ते\",\"Hello\"),\n", "    (\"धन्यवाद\",\"Thank you\"),\n", "    (\"आप कैसे हैं?\",\"How are you?\"),\n", "    (\"मैं छात्र हूं\",\"I am a student\")\n", "]"]}, {"cell_type": "code", "execution_count": 34, "id": "a338bd3d", "metadata": {}, "outputs": [], "source": ["src_texts = [pair[0] for pair in parallel_corpus]\n", "back_translated_texts = [pair[1] for pair in parallel_corpus]\n", "\n", "augmented_texts = parallel_corpus + [(bt, st) for st, bt in zip(back_translated_texts, translate(src_texts, tokenizer, model))]"]}, {"cell_type": "code", "execution_count": 35, "id": "72954615", "metadata": {}, "outputs": [], "source": ["train_src = [pair[0] for pair in augmented_texts]\n", "train_target = [pair[1] for pair in augmented_texts]"]}, {"cell_type": "code", "execution_count": null, "id": "384f26af", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["धन्यवाद -> Thank you\n", "आप कैसे हैं? -> How are you?\n", "नमस्ते -> Hello.\n"]}], "source": ["# tests = [\"Merci beaucoup\",\"Je suis content\",\"Merci beaucoup\",\"Bonjour\"]\n", "tests = [\"धन्यवाद\",\"आप कैसे हैं?\",\"नमस्ते\"]\n", "\n", "translations = translate(tests, tokenizer, model)\n", "\n", "for src, pred in zip(tests, translations):\n", "    print(f\"{src} -> {pred}\")"]}, {"cell_type": "code", "execution_count": null, "id": "747ac8d8", "metadata": {}, "outputs": [], "source": ["# test_src = [\"Merci beaucoup\",\"Comment ca va?\",\"Il fait bean aujourd'hui\",]\n", "test_src = [\"धन्यवाद\",\"आप कैसे हैं?\",\"मौसम अच्छा है आज\",]\n", "\n", "true_ref = [\"Thank you very much\",\"How are you?\",\"The weather is nice today\",]\n", "\n", "predictions = translate(test_src, tokenizer, model)"]}, {"cell_type": "code", "execution_count": 38, "id": "b718c590", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Using the latest cached version of the module from C:\\Users\\<USER>\\.cache\\huggingface\\modules\\evaluate_modules\\metrics\\evaluate-metric--bleu\\9e0985c1200e367cce45605ce0ecb5ede079894e0f24f54613fca08eeb8aff76 (last modified on Thu Apr 24 10:38:28 2025) since it couldn't be found locally at evaluate-metric--bleu, or remotely on the Hugging Face Hub.\n"]}], "source": ["bleu = evaluate.load(\"bleu\")\n", "r = bleu.compute(predictions=predictions,references=true_ref)"]}, {"cell_type": "code", "execution_count": 39, "id": "4c521c9f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Source: धन्यवाद\n", "Predicted: Thank you\n", "Reference: Thank you very much\n", "\n", "Source: आप कैसे हैं?\n", "Predicted: How are you?\n", "Reference: How are you?\n", "\n", "Source: मौसम अच्छा है आज\n", "Predicted: Weather is good today\n", "Reference: The weather is nice today\n", "\n"]}], "source": ["for src, pred, ref in zip(test_src, predictions, true_ref):\n", "    print(f\"Source: {src}\")\n", "    print(f\"Predicted: {pred}\")\n", "    print(f\"Reference: {ref}\")\n", "    print()\n", "    "]}, {"cell_type": "code", "execution_count": 40, "id": "62ec852a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["BLEU: 0.4307344840104562\n"]}], "source": ["print(f\"BLEU: {r['bleu']}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}