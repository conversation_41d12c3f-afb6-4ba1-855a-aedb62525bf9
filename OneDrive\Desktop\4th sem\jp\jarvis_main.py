#!/usr/bin/env python3
"""
🤖 JARVIS - Just A Rather Very Intelligent System
Marvel-Inspired AI Assistant

A comprehensive AI assistant system inspired by <PERSON>'s JARVIS
with voice recognition, system monitoring, and full laptop control.

Author: Your Personal AI Assistant
Version: 1.0
"""

import speech_recognition as sr
import pyttsx3
import psutil
import os
import subprocess
import platform
import socket
import time
import threading
import random
import datetime
import json
import webbrowser
import re
import tkinter as tk
from tkinter import ttk, scrolledtext
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np

class JarvisPersonality:
    """JARVIS personality and response system"""
    
    def __init__(self):
        self.greetings = [
            "Good morning, sir. How may I assist you today?",
            "At your service, sir. What can I do for you?",
            "Hello sir. JARVIS at your disposal.",
            "Good to see you again, sir. How may I be of assistance?"
        ]
        
        self.confirmations = [
            "Right away, sir.",
            "Consider it done, sir.",
            "Certainly, sir.",
            "Of course, sir.",
            "Immediately, sir."
        ]
        
        self.witty_responses = [
            "I'm sorry sir, I'm not quite sure I understand.",
            "Perhaps you could rephrase that, sir?",
            "I'm afraid that's beyond my current capabilities, sir.",
            "Interesting request, sir. Let me see what I can do."
        ]
        
        self.status_reports = [
            "All systems operational, sir.",
            "Everything is running smoothly, sir.",
            "No issues detected, sir.",
            "All systems green, sir."
        ]
    
    def get_greeting(self):
        hour = datetime.datetime.now().hour
        if hour < 12:
            return "Good morning, sir. JARVIS at your service."
        elif hour < 18:
            return "Good afternoon, sir. How may I assist you?"
        else:
            return "Good evening, sir. What can I do for you?"
    
    def get_confirmation(self):
        return random.choice(self.confirmations)
    
    def get_witty_response(self):
        return random.choice(self.witty_responses)
    
    def get_status_report(self):
        return random.choice(self.status_reports)


class JarvisVoice:
    """Voice recognition and text-to-speech system"""
    
    def __init__(self):
        # Initialize speech recognition
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        
        # Initialize text-to-speech
        self.tts_engine = pyttsx3.init()
        self.setup_voice()
        
        self.listening = False
    
    def setup_voice(self):
        """Configure JARVIS voice settings"""
        voices = self.tts_engine.getProperty('voices')
        
        # Try to find a British or sophisticated voice
        for voice in voices:
            if 'david' in voice.name.lower() or 'british' in voice.name.lower():
                self.tts_engine.setProperty('voice', voice.id)
                break
        
        # Set speech rate and volume
        self.tts_engine.setProperty('rate', 180)  # Slightly slower, more sophisticated
        self.tts_engine.setProperty('volume', 0.9)
    
    def speak(self, text):
        """Make JARVIS speak"""
        print(f"🤖 JARVIS: {text}")
        self.tts_engine.say(text)
        self.tts_engine.runAndWait()
    
    def listen_for_wake_word(self):
        """Listen for 'Hey JARVIS' or 'JARVIS'"""
        with self.microphone as source:
            self.recognizer.adjust_for_ambient_noise(source)
        
        print("🎤 Listening for wake word...")
        
        while True:
            try:
                with self.microphone as source:
                    audio = self.recognizer.listen(source, timeout=1, phrase_time_limit=3)
                
                text = self.recognizer.recognize_google(audio).lower()
                
                if 'jarvis' in text or 'hey jarvis' in text:
                    self.speak("Yes sir, I'm listening.")
                    return self.listen_for_command()
                    
            except sr.WaitTimeoutError:
                pass
            except sr.UnknownValueError:
                pass
            except sr.RequestError as e:
                print(f"Error with speech recognition: {e}")
    
    def listen_for_command(self):
        """Listen for actual command after wake word"""
        try:
            with self.microphone as source:
                print("🎤 Listening for command...")
                audio = self.recognizer.listen(source, timeout=5, phrase_time_limit=10)
            
            command = self.recognizer.recognize_google(audio)
            print(f"👤 You said: {command}")
            return command
            
        except sr.WaitTimeoutError:
            self.speak("I didn't hear anything, sir.")
            return None
        except sr.UnknownValueError:
            self.speak("I'm sorry sir, I didn't understand that.")
            return None
        except sr.RequestError as e:
            self.speak("I'm having trouble with my speech recognition, sir.")
            return None


class JarvisSystemMonitor:
    """System monitoring and hardware information"""
    
    def __init__(self):
        self.system_info = self.get_system_info()
        self.monitoring = False
        
    def get_system_info(self):
        """Get basic system information"""
        return {
            'platform': platform.system(),
            'platform_release': platform.release(),
            'platform_version': platform.version(),
            'architecture': platform.machine(),
            'hostname': socket.gethostname(),
            'ip_address': socket.gethostbyname(socket.gethostname()),
            'processor': platform.processor(),
            'ram': str(round(psutil.virtual_memory().total / (1024.0 **3)))+" GB"
        }
    
    def get_cpu_usage(self):
        """Get current CPU usage"""
        return psutil.cpu_percent(interval=1)
    
    def get_memory_usage(self):
        """Get current memory usage"""
        memory = psutil.virtual_memory()
        return {
            'total': memory.total,
            'available': memory.available,
            'percent': memory.percent,
            'used': memory.used,
            'free': memory.free
        }
    
    def get_disk_usage(self):
        """Get disk usage information"""
        disk_usage = []
        for partition in psutil.disk_partitions():
            try:
                partition_usage = psutil.disk_usage(partition.mountpoint)
                disk_usage.append({
                    'device': partition.device,
                    'mountpoint': partition.mountpoint,
                    'file_system': partition.fstype,
                    'total_size': partition_usage.total,
                    'used': partition_usage.used,
                    'free': partition_usage.free,
                    'percentage': (partition_usage.used / partition_usage.total) * 100
                })
            except PermissionError:
                continue
        return disk_usage
    
    def get_network_info(self):
        """Get network information"""
        network_info = psutil.net_io_counters()
        return {
            'bytes_sent': network_info.bytes_sent,
            'bytes_received': network_info.bytes_recv,
            'packets_sent': network_info.packets_sent,
            'packets_received': network_info.packets_recv
        }
    
    def get_running_processes(self, limit=10):
        """Get top running processes"""
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
            try:
                processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        # Sort by CPU usage
        processes.sort(key=lambda x: x['cpu_percent'] or 0, reverse=True)
        return processes[:limit]
    
    def get_system_status_report(self):
        """Generate comprehensive system status report"""
        cpu_usage = self.get_cpu_usage()
        memory = self.get_memory_usage()
        
        status = "excellent" if cpu_usage < 50 and memory['percent'] < 70 else "good" if cpu_usage < 80 and memory['percent'] < 85 else "concerning"
        
        report = f"""System Status Report:
        
CPU Usage: {cpu_usage}%
Memory Usage: {memory['percent']}%
Available Memory: {round(memory['available'] / (1024**3), 2)} GB
System Status: {status.title()}

Sir, your system is performing {status}."""
        
        return report


class JarvisCommandProcessor:
    """Natural language processing and command execution"""

    def __init__(self, voice_system, monitor_system, personality):
        self.voice = voice_system
        self.monitor = monitor_system
        self.personality = personality

        # Command patterns
        self.command_patterns = {
            'system_status': [r'system status', r'how.*system', r'system report', r'status report'],
            'time': [r'what.*time', r'current time', r'time'],
            'date': [r'what.*date', r'current date', r'date', r'today'],
            'weather': [r'weather', r'temperature', r'forecast'],
            'open_app': [r'open (.*)', r'launch (.*)', r'start (.*)'],
            'close_app': [r'close (.*)', r'quit (.*)', r'exit (.*)'],
            'search': [r'search (.*)', r'google (.*)', r'look up (.*)'],
            'cpu_usage': [r'cpu usage', r'processor usage', r'cpu'],
            'memory_usage': [r'memory usage', r'ram usage', r'memory'],
            'disk_space': [r'disk space', r'storage', r'hard drive'],
            'processes': [r'running processes', r'processes', r'tasks'],
            'shutdown': [r'shutdown', r'turn off', r'power off'],
            'restart': [r'restart', r'reboot'],
            'sleep': [r'sleep', r'hibernate'],
            'joke': [r'tell.*joke', r'joke', r'funny'],
            'greeting': [r'hello', r'hi', r'hey', r'good morning', r'good afternoon', r'good evening']
        }

        self.jokes = [
            "Why don't scientists trust atoms? Because they make up everything, sir.",
            "I told my wife she was drawing her eyebrows too high. She looked surprised, sir.",
            "Why don't programmers like nature? It has too many bugs, sir.",
            "I'm reading a book about anti-gravity. It's impossible to put down, sir."
        ]

    def process_command(self, command):
        """Process and execute voice commands"""
        if not command:
            return

        command = command.lower().strip()

        # Find matching command pattern
        for cmd_type, patterns in self.command_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, command)
                if match:
                    return self.execute_command(cmd_type, match, command)

        # If no pattern matches
        self.voice.speak(self.personality.get_witty_response())

    def execute_command(self, cmd_type, match, original_command):
        """Execute the identified command"""
        try:
            if cmd_type == 'greeting':
                self.voice.speak(self.personality.get_greeting())

            elif cmd_type == 'system_status':
                report = self.monitor.get_system_status_report()
                self.voice.speak(report)

            elif cmd_type == 'time':
                current_time = datetime.datetime.now().strftime("%I:%M %p")
                self.voice.speak(f"The current time is {current_time}, sir.")

            elif cmd_type == 'date':
                current_date = datetime.datetime.now().strftime("%A, %B %d, %Y")
                self.voice.speak(f"Today is {current_date}, sir.")

            elif cmd_type == 'cpu_usage':
                cpu = self.monitor.get_cpu_usage()
                self.voice.speak(f"Current CPU usage is {cpu} percent, sir.")

            elif cmd_type == 'memory_usage':
                memory = self.monitor.get_memory_usage()
                self.voice.speak(f"Memory usage is at {memory['percent']} percent, sir. {round(memory['available'] / (1024**3), 2)} gigabytes available.")

            elif cmd_type == 'open_app':
                app_name = match.group(1) if match.groups() else None
                if app_name:
                    self.open_application(app_name)

            elif cmd_type == 'search':
                query = match.group(1) if match.groups() else None
                if query:
                    self.search_web(query)

            elif cmd_type == 'joke':
                joke = random.choice(self.jokes)
                self.voice.speak(joke)

            elif cmd_type == 'shutdown':
                self.voice.speak("Initiating shutdown sequence, sir. Goodbye.")
                os.system("shutdown /s /t 10")

            elif cmd_type == 'restart':
                self.voice.speak("Initiating restart sequence, sir.")
                os.system("shutdown /r /t 10")

            else:
                self.voice.speak(self.personality.get_confirmation())

        except Exception as e:
            self.voice.speak(f"I encountered an error while executing that command, sir. {str(e)}")

    def open_application(self, app_name):
        """Open applications by name"""
        app_name = app_name.lower()

        app_mappings = {
            'notepad': 'notepad.exe',
            'calculator': 'calc.exe',
            'chrome': 'chrome.exe',
            'firefox': 'firefox.exe',
            'edge': 'msedge.exe',
            'word': 'winword.exe',
            'excel': 'excel.exe',
            'powerpoint': 'powerpnt.exe',
            'file explorer': 'explorer.exe',
            'task manager': 'taskmgr.exe',
            'control panel': 'control.exe',
            'command prompt': 'cmd.exe',
            'powershell': 'powershell.exe'
        }

        if app_name in app_mappings:
            try:
                subprocess.Popen(app_mappings[app_name])
                self.voice.speak(f"Opening {app_name}, sir.")
            except Exception as e:
                self.voice.speak(f"I couldn't open {app_name}, sir. {str(e)}")
        else:
            self.voice.speak(f"I'm not sure how to open {app_name}, sir.")

    def search_web(self, query):
        """Search the web"""
        try:
            url = f"https://www.google.com/search?q={query.replace(' ', '+')}"
            webbrowser.open(url)
            self.voice.speak(f"Searching for {query}, sir.")
        except Exception as e:
            self.voice.speak(f"I couldn't perform that search, sir. {str(e)}")


if __name__ == "__main__":
    print("🤖 JARVIS System Components Loaded")
    print("Run 'python jarvis_main.py' to start the full system")
