{"cells": [{"cell_type": "code", "execution_count": null, "id": "b50beafb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tokenize: ['Hi', ',', 'I', 'am', '<PERSON><PERSON>h', '.', 'I', 'was', 'nice', 'meeting', 'you', '.', 'I', 'am', 'usn', '1dt22ai004', '.']\n", "filtered tokens ['Hi', 'I', 'am', '<PERSON><PERSON><PERSON>', 'I', 'was', 'nice', 'meeting', 'you', 'I', 'am', 'usn', '1dt22ai004']\n"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["#Program 1\n", "import nltk, re\n", "from nltk.corpus import stopwords\n", "from nltk.tokenize import word_tokenize\n", "from nltk.stem import PorterStemmer\n", "\n", "s=\"Hi, I am <PERSON><PERSON><PERSON>. I was nice meeting you. I am usn 1dt22ai004.\"\n", "\n", "print(\"Tokenize:\" ,word_tokenize(s))\n", "print(\"filtered tokens\" , [word for word in word_tokenize(s) if word.isalnum()])"]}, {"cell_type": "code", "execution_count": null, "id": "276a2e5b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 5}