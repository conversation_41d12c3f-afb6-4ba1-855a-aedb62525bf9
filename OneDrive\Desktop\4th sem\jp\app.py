# FILEPATH: app.py
from flask import Flask, render_template, Response, request
import cv2
from model import detect_objects
from alert import send_alert

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/video_feed')
def video_feed():
    def generate():
        cap = cv2.VideoCapture(0)  # Replace with actual video feed source
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            frame = detect_objects(frame)
            _, buffer = cv2.imencode('.jpg', frame)
            frame = buffer.tobytes()
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
        cap.release()

    return Response(generate(), mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/send_alert', methods=['POST'])
def alert():
    message = request.form['message']
    status_code = send_alert(message)
    return f"Alert sent with status code {status_code}"

if __name__ == '__main__':
    app.run(debug=True)
